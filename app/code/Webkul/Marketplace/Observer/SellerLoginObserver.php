<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Webkul\Marketplace\Observer;

use Magento\Framework\Event\ObserverInterface;

class SellerLoginObserver implements ObserverInterface
{
    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    protected $_helper;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_session;

    /**
     * Customer data
     *
     * @var \Magento\Customer\Model\Url
     */
    protected $_customerUrl;

    /**
     * @var \Magento\Framework\UrlInterface
     */
    protected $_urlBuilder;

    /**
     * @param \Webkul\Marketplace\Helper\Data $helper
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Customer\Model\Url $customerUrl
     * @param \Magento\Framework\UrlInterface $urlBuilder
     */
    public function __construct(
        \Webkul\Marketplace\Helper\Data $helper,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Model\Url $customerUrl,
        \Magento\Framework\UrlInterface $urlBuilder
    ) {
        $this->_helper = $helper;
        $this->_session = $customerSession;
        $this->_customerUrl = $customerUrl;
        $this->_urlBuilder = $urlBuilder;
    }

    /**
     * Check captcha on user login page
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @throws NoSuchEntityException
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $logger->info('=== SellerLoginObserver ===');
        $logger->info('Is separate panel: ' . ($this->_helper->getIsSeparatePanel() ? 'YES' : 'NO'));

        if ($this->_helper->getIsSeparatePanel()) {
            $controller = $observer->getControllerAction();
            $vendorLogin = $controller->getRequest()->getPost('vendor_login');
            $logger->info('Vendor login param: ' . ($vendorLogin ?: 'NULL'));

            if ($vendorLogin) {
                $url = $this->_urlBuilder->getUrl("marketplace/account/dashboard");
                $logger->info('Setting before/after auth URL to: ' . $url);
                $this->_session->setBeforeAuthUrl($url);
                $this->_session->setAfterAuthUrl($url);
            }
        }

        return $this;
    }
}
