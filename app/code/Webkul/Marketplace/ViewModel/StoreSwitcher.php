<?php
/**
 * Store Switcher ViewModel
 *
 * Handles business logic for store switching with currency information
 */
namespace Webkul\Marketplace\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Model\Store;

class StoreSwitcher implements ArgumentInterface
{
    /**
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private StoreManagerInterface $storeManager
    ) {
    }

    /**
     * Get current store with currency information
     *
     * @param array $stores
     * @param int $currentStoreId
     * @return array
     */
    public function getCurrentStoreData(array $stores, int $currentStoreId): array
    {
        $currentStore = null;
        $currencyCode = '';
        
        foreach ($stores as $store) {
            if ($store->getId() == $currentStoreId) {
                $currentStore = $store;
                $currencyCode = $store->getDefaultCurrency()->getCode();
                break;
            }
        }
        
        return [
            'store' => $currentStore,
            'currency_code' => $currencyCode
        ];
    }

    /**
     * Get store currency code
     *
     * @param Store $store
     * @return string
     */
    public function getStoreCurrencyCode(Store $store): string
    {
        if (!$store) {
            return '';
        }
        
        return $store->getDefaultCurrency()->getCode();
    }

    /**
     * Check if store has currency
     *
     * @param Store $store
     * @return bool
     */
    public function hasStoreCurrency(Store $store): bool
    {
        return !empty($this->getStoreCurrencyCode($store));
    }
}
