<?php
/**
 * Language switcher with currency
 *
 * @var \Magento\Store\Block\Switcher $block
 * @var \Webkul\Marketplace\ViewModel\StoreSwitcher $storeSwitcherViewModel
 * @var \Magento\Framework\Escaper $escaper
 */
?>
<?php if (count($block->getStores()) > 1): ?>
    <?php $stores = $block->getStores(); ?>
    <?php $currentStoreId = $block->getCurrentStoreId(); ?>
    <?php $storeSwitcherViewModel = $block->getData('storeSwitcherViewModel'); ?>
    
    <div class="wk-mp-language-switcher wk-mp-dropdown wk-mp__action-dropdown-wrap">
        <a href="#"
           onclick="return false;"
           class="wk-mp__action-dropdown"
           title="<?= $escaper->escapeHtml(__('Select Store')) ?>"
           data-mage-init='{"dropdown":{}}'
           data-toggle="dropdown">
            <span class="wk-mp__action-dropdown-text">
                <?php 
                if ($storeSwitcherViewModel) {
                    $currentStoreData = $storeSwitcherViewModel->getCurrentStoreData($stores, $currentStoreId);
                    $currentStore = $currentStoreData['store'];
                    $currencyCode = $currentStoreData['currency_code'];
                } else {
                    $currencyCode = '';
                }
                ?>
                <span><?= $escaper->escapeHtml($block->getStoreName()) ?><?= $currencyCode ? ' (' . $escaper->escapeHtml($currencyCode) . ')' : '' ?></span>
            </span>
        </a>
        <ul class="wk-mp__action-dropdown-menu">
            <?php foreach ($stores as $store): ?>
                <?php if ($store->getId() != $currentStoreId): ?>
                    <?php if ($storeSwitcherViewModel && $storeSwitcherViewModel->hasStoreCurrency($store)): ?>
                        <?php $storeCurrencyCode = $storeSwitcherViewModel->getStoreCurrencyCode($store); ?>
                        <li class="wk-mp-dropdown-menu-store">
                            <?php $postData = $block->getTargetStorePostData($store); ?>
                            <a href="#" 
                               data-post='<?= /* @noEscape */ $postData ?>'
                               title="<?= $escaper->escapeHtml($store->getName()) ?> (<?= $escaper->escapeHtml($storeCurrencyCode) ?>)">
                                <?= $escaper->escapeHtml($store->getName()) ?> (<?= $escaper->escapeHtml($storeCurrencyCode) ?>)
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>
