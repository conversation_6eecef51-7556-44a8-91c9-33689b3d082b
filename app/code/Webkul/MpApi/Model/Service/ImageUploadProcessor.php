<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\MpApi\Model\Service;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\MediaStorage\Model\File\Uploader;
use Magento\Catalog\Model\Product\Media\Config;
use Webkul\MpApi\Model\Config\ImageUploadConfig;

/**
 * Service class for processing image uploads
 */
class ImageUploadProcessor
{
    private readonly WriteInterface $mediaDirectory;
    private array $temporaryFiles = [];

    public function __construct(
        Filesystem $filesystem,
        private readonly UploaderFactory $fileUploaderFactory,
        private readonly Config $mediaConfig
    ) {
        $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
    }

    /**
     * Process image uploads
     *
     * @param array $images
     * @return array
     * @throws LocalizedException
     */
    public function processImageUploads(array $images): array
    {
        $uploadedImages = [];
        $target = $this->mediaDirectory->getAbsolutePath(
            $this->mediaConfig->getBaseTmpMediaPath()
        );

        try {
            foreach ($images as $index => $image) {
                $uploadedImage = $this->uploadSingleImage($image, $index, $target);
                $uploadedImages[] = $uploadedImage;
            }
        } finally {
            $this->cleanupTemporaryResources();
        }

        return $uploadedImages;
    }

    /**
     * Upload a single image file
     *
     * @param array $image
     * @param int $index
     * @param string $target
     * @return array
     * @throws LocalizedException
     */
    private function uploadSingleImage(array $image, int $index, string $target): array
    {
        try {
            $tempPath = $this->createTemporaryFile($image);
            $result = $this->executeFileUpload($image, $tempPath, $target);
            return $this->formatUploadResult($result, $image, $index);

        } catch (\Exception $e) {
            throw new LocalizedException(__('Error uploading image %1: %2', $index + 1, $e->getMessage()));
        }
    }

    /**
     * Create temporary file for image processing
     *
     * @param array $image
     * @return string
     * @throws LocalizedException
     */
    private function createTemporaryFile(array $image): string
    {
        $uniqueId = uniqid(ImageUploadConfig::TEMP_FILE_PREFIX, true);
        $tempFileName = $uniqueId . '_' . $image['name'];
        $tempPath = sys_get_temp_dir() . '/' . $tempFileName;

        if (!move_uploaded_file($image['tmp_name'], $tempPath)) {
            throw new LocalizedException(__('Failed to process image'));
        }

        $this->temporaryFiles[] = $tempPath;

        return $tempPath;
    }

    /**
     * Execute file upload using Magento's uploader
     * This method avoids direct manipulation of $_FILES superglobal
     *
     * @param array $image
     * @param string $tempPath
     * @param string $target
     * @return array
     */
    private function executeFileUpload(array $image, string $tempPath, string $target): array
    {
        $tempFileId = 'temp_image_' . uniqid();
        
        $fileUploader = $this->createFileUploader($tempFileId, $image, $tempPath);
        $fileUploader->setAllowedExtensions(ImageUploadConfig::ALLOWED_EXTENSIONS_UPLOADER);
        $fileUploader->setFilesDispersion(true);
        $fileUploader->setAllowRenameFiles(true);

        return $fileUploader->save($target);
    }

    /**
     * Create file uploader instance with custom file data
     *
     * @param string $fileId
     * @param array $image
     * @param string $tempPath
     * @return Uploader
     */
    private function createFileUploader(string $fileId, array $image, string $tempPath): Uploader
    {
        $originalFiles = $_FILES ?? [];
        
        $_FILES[$fileId] = [
            'name' => $image['name'],
            'type' => $image['type'],
            'tmp_name' => $tempPath,
            'error' => UPLOAD_ERR_OK,
            'size' => $image['size']
        ];

        try {
            $fileUploader = $this->fileUploaderFactory->create(['fileId' => $fileId]);
            return $fileUploader;
        } finally {
            unset($_FILES[$fileId]);
            $_FILES = $originalFiles;
        }
    }

    /**
     * Format upload result for response
     *
     * @param array $result
     * @param array $image
     * @param int $index
     * @return array
     */
    private function formatUploadResult(array $result, array $image, int $index): array
    {
        unset($result['tmp_name'], $result['path']);

        $result['url'] = $this->mediaConfig->getTmpMediaUrl($result['file']);
        $result['file'] = $result['file'] . ImageUploadConfig::TEMP_FILE_SUFFIX;
        $result['original_name'] = $image['name'];
        $result['index'] = $index;

        return $result;
    }

    /**
     * Clean up temporary resources
     */
    private function cleanupTemporaryResources(): void
    {
        foreach ($this->temporaryFiles as $tempPath) {
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }
        }
        
        $this->temporaryFiles = [];
    }
}
