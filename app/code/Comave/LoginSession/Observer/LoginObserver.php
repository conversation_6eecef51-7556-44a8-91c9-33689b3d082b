<?php
namespace Comave\LoginSession\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Firebase\JWT\JWT;
use Magento\Framework\App\Config\ScopeConfigInterface;

class LoginObserver implements ObserverInterface
{
    protected $cookieManager;
    protected $customerRepository;
    protected $cookieMetadataFactory;
    protected $scopeConfig;
    
    public function __construct(
        CookieManagerInterface $cookieManager,
        CookieMetadataFactory $cookieMetadataFactory,
        CustomerRepositoryInterface $customerRepository,
        ScopeConfigInterface $scopeConfig  
    ) {
        $this->cookieManager = $cookieManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        $this->scopeConfig = $scopeConfig;
        $this->customerRepository = $customerRepository;  
    }

    public function execute(Observer $observer)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $logger->info('=== LoginSession Observer (SHOULD BE DISABLED) ===');
        $logger->info('Observer executed - this should not happen if disabled properly');

        $directoryName = $_SERVER['SERVER_NAME'];
        $logger->info('base path'. $directoryName);
        $logger->info('server ='. print_r($_SERVER['SERVER_NAME'], true));
        $enabled = $this->scopeConfig->getValue('comave_loginsession/general/enabled');
        $logger->info('Config enabled: ' . ($enabled ? 'YES' : 'NO'));

        $this->cookieManager->setPublicCookie('login-ob', ' this istest');
        if (!$enabled) {
            $logger->info('Config disabled - returning early');
            return;
        }

        $customer = $observer->getEvent()->getCustomer();
        $identityfireId = $this->scopeConfig->getValue('comave_loginsession/general/identityfire_id');
        $privateKeyPath = BP . '/app/code/Comave/LoginSession/private_key.pem';            
        $privateKey = file_get_contents($privateKeyPath);
        
        $jwksFilePath = BP .'/pub/media/.well-known/jwks.json';
        $jwksContent = file_get_contents($jwksFilePath);
        // Decode the JSON content
        $jwks = json_decode($jwksContent, true);
        if (!isset($jwks['kid'])) {
            $logger->err('JWKS JSON does not contain "kid" key.');
            return;
        }
        $kid = $jwks['kid'];
        $logger->info('kid'. $kid);
        $header = [
            'typ' => 'JWT',
            'alg' => 'RS256',
            'kid' => $kid
            ];
        
        $userData = [
            'user_id' => $customer->getId(),
            'email' => $customer->getEmail()
        ];
        $now = time();
        $expTime = $now + (60 * 60);
        $payload = [
            'iss' => $identityfireId, // Issuer
            'aud' => $directoryName, // Audience
            'kid' => $kid,
            'iat' => $now, // Issued at
            'nbf' => $now, // Not before
            'exp' => $expTime, // Expiry (4 hours from now)
            'data' => $userData
        ];

        $jwt = JWT::encode($payload, $privateKey, 'RS256', null, $header);
        $customerId = $customer->getId(); 
        $customeratt = $this->customerRepository->getById($customerId);
        $customeratt->setCustomAttribute("jwttoken", $jwt);
        $this->customerRepository->save($customeratt);
        $logger->info('Payload = '. print_r($payload, true));
        $logger->info('JWT = '. $jwt);
        $metadata = $this->cookieMetadataFactory->createPublicCookieMetadata()
            ->setDuration(60 * 60)
            ->setPath('/')
            ->setDomain($directoryName)
            ->setHttpOnly(false)
            ->setSameSite('Lax'); // Set SameSite attribute as 'Lax'
        $this->cookieManager->setPublicCookie('login-session', $jwt, $metadata);
    }
}
