<?php

declare(strict_types=1);

namespace Comave\DisableFrontend\Plugin;

use Comave\DisableFrontend\Model\ConfigProvider;
use Magento\Framework\App\Area;
use Magento\Framework\App\AreaList;
use Magento\Framework\App\FrontControllerInterface;
use Magento\Framework\App\Request\Http as RequestHttp;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\ForwardFactory;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Controller\Result\RedirectFactory;
use Psr\Log\LoggerInterface;

class DisableFrontend
{
    /**
     * @param AreaList $areaList
     * @param RequestHttp $request
     * @param ConfigProvider $configProvider
     * @param ForwardFactory $forwardFactory
     * @param CustomerSession $customerSession
     * @param RedirectFactory $resultRedirectFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly AreaList $areaList,
        private readonly RequestHttp $request,
        private readonly ConfigProvider $configProvider,
        private readonly ForwardFactory $forwardFactory,
        private readonly CustomerSession $customerSession,
        private readonly RedirectFactory $resultRedirectFactory,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @param FrontControllerInterface $subject
     * @param callable $next
     * @param RequestInterface $requestInterface
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function aroundDispatch(
        FrontControllerInterface $subject,
        callable $next,
        RequestInterface $requestInterface
    ) {
        if (!$this->configProvider->isDisableFrontendActive()) {
            return $next($requestInterface);
        }

        $frontName = $this->request->getFrontName();
        $areaCode = $this->areaList->getCodeByFrontName($frontName);

        // allow any not frontend call
        if ($areaCode !== Area::AREA_FRONTEND) {
            return $next($requestInterface);
        }

        $allowedFrontNameList = $this->configProvider->getAllowedFrontNameList();
        $pathInfo = ltrim($this->request->getPathInfo(), '/');

        if (empty($allowedFrontNameList)) {
            return $next($requestInterface);
        }

        $found = false;

        foreach ($allowedFrontNameList as $allowedFrontName) {
            if (!str_contains($allowedFrontName, $frontName)
                || (!empty($pathInfo) && !$this->isSubstring($allowedFrontName, $pathInfo))
            ) {
                continue;
            }

            $found = !stripos($allowedFrontName, '*')
                || preg_match($this->toRegex($allowedFrontName), $pathInfo);
            break;
        }

        // allow any frontend call with frontName from allowlist
        if ($found) {
            return $next($requestInterface);
        }

        // block any frontend call that is not in config values
        $debugMsg = "Request blocked: $pathInfo |  {$this->request->getPathInfo()} | {$this->request->getFrontName()}";
        $this->logger->debug($debugMsg);

        // Add detailed logging
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $isLoggedIn = $this->customerSession->isLoggedIn();
        $isSellerData = $this->customerSession->getData('is_seller');
        $customerId = $this->customerSession->getCustomerId();

        $logger->info('=== DisableFrontend Plugin - Blocking Request ===');
        $logger->info('Path Info: ' . $pathInfo);
        $logger->info('Request Path: ' . $this->request->getPathInfo());
        $logger->info('Front Name: ' . $this->request->getFrontName());
        $logger->info('Customer ID: ' . ($customerId ?: 'NULL'));
        $logger->info('Is Logged In: ' . ($isLoggedIn ? 'YES' : 'NO'));
        $logger->info('Is Seller Data: ' . ($isSellerData ? 'YES' : 'NO'));
        $logger->info('Is Seller Logged In: ' . ($this->isSellerLoggedIn() ? 'YES' : 'NO'));

        $path = $this->isSellerLoggedIn()
            ? 'marketplace/account/dashboard/'
            : 'marketplace/account/login/';

        $logger->info('Redirecting to: ' . $path);

        return $this->resultRedirectFactory->create()->setPath($path);
    }

    /**
     * @param string $allowedPath
     * @return string
     */
    private function toRegex(string $allowedPath): string
    {
        $escaped = preg_quote($allowedPath, '/');
        $regex = str_replace('\*', '.*', $escaped);
        $regex = rtrim($regex, '\/.*') . '(\/.*)?';

        return '/^' . $regex . '$/';
    }

    /**
     * Check if $allowedFrontName is substring at the beginning of the $pathInfo
     *
     * @param string $allowedFrontName
     * @param string $pathInfo
     * @return bool
     */
    private function isSubstring(string $allowedFrontName, string $pathInfo): bool
    {
        $basePath = rtrim(explode('*', $allowedFrontName)[0], '/');

        return strpos($pathInfo, $basePath) === 0;
    }

    /**
     * @return bool
     */
    private function isSellerLoggedIn(): bool
    {
        return $this->customerSession->isLoggedIn()
            && (bool)$this->customerSession->getData('is_seller');
    }
}
