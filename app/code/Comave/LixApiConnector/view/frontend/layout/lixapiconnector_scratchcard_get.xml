<?xml version="1.0"?>
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/> 
    <body> 
        <referenceBlock name="page.main.title" remove="true"/> 
        <referenceContainer name="content">
            <block class="Comave\LixApiConnector\Block\Scratchcard\View" name="scratchcard_view" template="Comave_LixApiConnector::scratchcard/view.phtml"/>
        </referenceContainer>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="test" template="Comave_LixApiConnector::registerpopup.phtml"></block>
        </referenceContainer>
    </body>
</page>