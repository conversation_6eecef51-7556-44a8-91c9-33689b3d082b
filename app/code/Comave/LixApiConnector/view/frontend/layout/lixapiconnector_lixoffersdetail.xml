<?xml version="1.0"?>
<!--
/**
 *
 * @package   SK\CustomerAccountTab
 * <AUTHOR> <<EMAIL>>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/> 
    <body> 
        <referenceBlock name="page.main.title" remove="true"/>  
        <referenceContainer name="content"> 
            <block class="Comave\LixApiConnector\Block\Offers" name="marketplace_lixoffers_tab" template="Comave_LixApiConnector::Marketplace/lixoffersdetail.phtml" />
        </referenceContainer> 
    </body>
</page>
