<?xml version="1.0"?>
<!--
/**
 *
 * @package   SK\CustomerAccountTab
 * <AUTHOR> <kish<PERSON><EMAIL>>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/> 
    <body> 
        <referenceBlock name="page.main.title"> 
            <action method="setPageTitle"> 
                <argument translate="true" name="title" xsi:type="string">Set Pin</argument> 
            </action> 
        </referenceBlock> 
        <referenceContainer name="content"> 
            <block class="Comave\LixApiConnector\Block\Updatepin" name="custom_tab_sk" template="Comave_LixApiConnector::updatepin.phtml" />
        </referenceContainer> 
    </body>
</page>
