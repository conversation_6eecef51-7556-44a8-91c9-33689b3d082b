<?php
$walletblock= $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Offers');
$details = $block->getWalletListDetails();
$charity = $block->getCharitylistDetails();
if(isset($charity) && $charity['success'] == 1){ 
    $charityData = $charity['data'];   
}
if(isset($details) && $details['success'] == 1){ 
    $detailsData = $details['data'];   
}
?>
<div class="charity-details">
    
        <div class="user-wallets-list">
            <label><?= $block->escapeHtml(__('Select wallet')) ?></label>
            <div class="wallets-list-wrapper">
                <select name="wallet_id" id="wallet_id">
                    <option value="">
                        <?= $block->escapeHtml(__('Select wallet')) ?>
                    </option>
                    <?php 
                    if (isset($detailsData)) {
                        foreach($detailsData as $detail){ 
                           ?>
                            <option value="<?php echo $detail['custom_currency_id']; ?>" data-balance="<?php echo $detail['balance']; ?>">
                                <?php echo $detail['currency_symbol']. " - ".$detail['balance'] ; ?>
                            </option>
                            <?php
                        }
                    } else {
                        ?>
                            <option value="">
                                <?php echo $block->escapeHtml(__('No wallets are assigned')); ?>
                            </option>
                    <?php
                    }
                ?>
                </select>
            </div>
        </div> 
        <div id="wallet-error-message"></div>
        <div id="balance-display"></div> 
        <div class="charity-list">
            <label><?= $block->escapeHtml(__('Select charity foundation')) ?></label>
            <div class="charity-list-wrapper">
                <select name="charity_id" id="charity_id">
                    <option value="">
                        <?= $block->escapeHtml(__('Select charity foundation')) ?>
                    </option>
                    <?php 
                    if (isset($charityData)) {
                        foreach($charityData as $charities){ 
                           ?>
                            <option value="<?php echo $charities['id']; ?>">
                                <?= $escaper->escapeHtml($charities['name']); ?>
                            </option>
                            <?php
                        }
                    } else {
                        ?>
                            <option value="">
                                <?php echo $block->escapeHtml(__('No charity foundation available')); ?>
                            </option>
                    <?php
                    }
                ?>
                </select>
            </div>
        </div>
        <div id="charity-error-message"></div>
        <div id="charity-errormsg"></div>             
        <div class="charity-amount">
            <label><?= $block->escapeHtml(__('Amount')) ?></label>
            <div class="charity-amount-wrapper">
                <input type="text" id="amount" name="amount">
            </div>
        </div>
        <div id="amount-error-message"></div> 
        <div id="rem-balance-display"></div> 
        <div id="charity-successmsg"></div>
        <div class="submit-charity-form">
            <button id="donateButton"><?= $block->escapeHtml(__('Donate')) ?></button>
        </div>
    
</div>
<!-- Loader element  -->
<div id="charity-loader" style="display: none;">
    <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>"
        alt="Loading...">
</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        var walletSelect = document.getElementById("wallet_id");
        var amount = document.getElementById("amount");
        var balanceDisplay = document.getElementById("balance-display");
        var rembalanceDisplay = document.getElementById("rem-balance-display");
        var walleterrorMessage = document.getElementById('wallet-error-message');
        var amounterrorMessage = document.getElementById('amount-error-message');
        var donateButton = document.getElementById("donateButton");

        // Function to disable the donate button
        function disableDonateButton() {
            donateButton.disabled = true;
        }

        // Function to enable the donate button
        function enableDonateButton() {
            donateButton.disabled = false;
        }
        walletSelect.addEventListener("change", function() {
            var selectedOption = this.options[this.selectedIndex];
            var balance = selectedOption.getAttribute("data-balance");
            if (balance <= 0) {
                balanceDisplay.textContent = '';
                walleterrorMessage.textContent = 'Selected wallet has insufficient balance. Please select another wallet.';
            } else {
                walleterrorMessage.textContent = '';
                balanceDisplay.textContent = "Available balance: "+balance;
            }
        });

        amount.addEventListener('input', function (event) {
            var inputValue = event.target.value;
            var selectedWallet = walletSelect.options[walletSelect.selectedIndex];
            var walletBalance = parseFloat(selectedWallet.getAttribute("data-balance"));
            if (!isNaN(inputValue) && inputValue > 0) {
                var remainingBalance = walletBalance - inputValue;
                if (remainingBalance < 0) {
                    rembalanceDisplay.textContent = "Insufficient funds";
                } else {
                    rembalanceDisplay.textContent = "Remaining balance: " + remainingBalance.toFixed(2);
                }
            } else {
                rembalanceDisplay.textContent = "";
                amounterrorMessage.innerHTML += 'Please enter a valid amount greater than 0.';
                event.preventDefault();
            }
        });
    });
    require(['jquery'], function ($) {
        $(document).ready(function () {
                    // Function to validate form
        function validateForm() {
            var walletSelect = document.getElementById('wallet_id').value;
            var amountInput = document.getElementById('amount').value;
            var charitySelect = document.getElementById('charity_id').value;
            var isValid = true;

            // Wallet validation
            if (walletSelect === '') {
                document.getElementById('wallet-error-message').textContent = 'Please select a wallet.';
                isValid = false;
            }

            // Amount validation
            var amount = parseFloat(amountInput);
            if (isNaN(amount) || amount <= 0) {
                document.getElementById('amount-error-message').textContent = 'Please enter a valid amount greater than 0.';
                isValid = false;
            }

            // Charity validation
            if (charitySelect === '') {
                document.getElementById('charity-error-message').textContent = 'Please select a charity foundation.';
                isValid = false;
            }

            return isValid;
        }

            $('.submit-charity-form button').click(function (e) {
                e.preventDefault();
                var walletSelect = document.getElementById('wallet_id').value;
                var amountInput = document.getElementById('amount').value;
                var charitySelect = document.getElementById('charity_id').value;
                if (validateForm()) {
                    var formData = {
                        wallet_id: walletSelect,
                        amount: amountInput,
                        charity_id: charitySelect
                    };
                    $.ajax({
                        url: "<?php echo $block->getChatity(); ?>",
                        method: 'POST',
                        data: formData,
                        beforeSend: function () {
                            $('#charity-loader').show();
                        },
                        success: function (response) {
                            if (response.status == "success") {
                                document.getElementById("charity-successmsg").innerHTML = response.responseData.message;
                            }
                            else {
                                document.getElementById("charity-errormsg").innerHTML = response.responseData.message;
                                return;
                            }

                        },
                        error: function (xhr, status, error) {
                        },
                        complete: function () {
                            $('#charity-loader').hide();
                        }
                    });
                }
            });
        });
    });
</script>