<?php
$predictionTasks = $block->getPredictListMatches();

$userpredictlist = $block->getPredictListUsers();
$predictions = isset($userpredictlist['predictions']) ? $userpredictlist['predictions'] : [];

$currentMatches = []; // Array to store current matches
$previousMatches = []; // Array to store previous matches

$currentDateTime = new DateTime(); // Get current date and time
$currentDate = $currentDateTime->format('Y-m-d');

$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$storeManager = $objectManager->get('Magento\Store\Model\StoreManagerInterface');
$mediaUrl = $storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);

//echo "Number of predictions: " . count($predictions) . "<br>";

foreach ($predictions as $task) {
    $matchDateTime = new DateTime($task['match_date']);

    // Compare dates only (ignore time) to determine current and previous matches
    $matchDate = $matchDateTime->format('Y-m-d');
    
    //echo "Task Id: {$task['task_id']}, Match Date: {$matchDate}, Current Date: {$currentDate}<br>";
    //echo "+++++<br>";
    //print_r($matchDateTime);
    //print_r($currentDateTime);
    
    //echo "Task Id: {$task['task_id']}, Match Date Time: {$matchDateTime}, Current Date Time: {$currentDateTime}<br>";
    if ($task['match_status'] === 'Scheduled' || $task['match_status'] === 'In Play') {
        // Matches scheduled for today or in the future are considered current
        $currentMatches[] = $task;
    } elseif ($task['match_status'] === 'Finished' || $task['match_status'] === 'Cancelled' ) {
        // Matches in the past or finished are considered previous
        $previousMatches[] = $task;
    }
}
// Sort previous matches by match_date in descending order
usort($currentMatches, function($a, $b) {
    return strtotime($b['match_date']) - strtotime($a['match_date']);
});

//echo "<pre>";
//print_r($currentMatches);
//echo "</pre>";

// Sort previous matches by match_date in descending order
usort($previousMatches, function($a, $b) {
    return strtotime($b['match_date']) - strtotime($a['match_date']);
});

//echo "<pre>";
//print_r($previousMatches);
//echo "</pre>";
?>
<section class="predict-score-head">
</section>
<section class="container-fluid predict-data">
    <div class="predict-tab">
        <div class="container tabs">
            <ul class="tab-list col-md-12">
                <li id="select-1" class="active">
                    <h3>Start Game</h3>
                </li>
                <li id="select-2">
                    <h3>My Prediction</h3>
                </li>
            </ul>
        </div>
        <div class="tab-content-one">
            <div class="tabs-content active" id="content-select-1">
                <h1 class="tab-title">Guess the Score of upcoming matches</h1>
                <h5 class="sub-head">Choose one upcoming match to predict the score</h5>
                <h3 class="utc-now">UTC Now</h3>
                <div class="success-message">Prediction submitted successfully!</div>
                <?php if (!empty($predictionTasks)): ?>
                <ul class="predict-list-matches">
                    <?php foreach ($predictionTasks as $task):?>
                    <li class="pre-listing-matches" data-task-id="<?php echo $task['task_id']; ?>"
                        data-predict-date-schedule-local-time="<?php echo $task['predict_date_schedule_local_time']; ?>">
                            <div class="ongoing-data">  
                                <div class="match-schedule"></div>
                                <div class="card-top">
                                    <div class="match-date" data-utc-time="<?php echo $task['match_date']; ?>" data-match-status="<?php echo $task['match_status']; ?>">
                                    </div>
                                    <div class="no-prediction"></div>
                                </div>
                            </div>
                            <div class="match-teams">
                                <span class="card-logo-name">
                                    <img src="<?php echo $task['team_home_logo']; ?>" alt="<?php echo $task['team_home_name']; ?>">
                                    <p class="team-name"><?php echo $task['team_home_name']; ?></p>
                                </span>
                                <div class="score-input">
                                    <button type="button" class="increment">+</button>
                                    <input type="text" name="home_team_score" value="0" min="0">
                                    <button type="button" class="decrement">-</button>
                                </div>
                                <div class="score-input">
                                    <button type="button" class="increment">+</button>
                                    <input type="text" name="away_team_score" value="0" min="0">
                                    <button type="button" class="decrement">-</button>
                                </div>
                                <span class="card-logo-name">
                                    <img src="<?php echo $task['team_away_logo']; ?>" alt="<?php echo $task['team_away_name']; ?>">
                                    <p class="team-name"><?php echo $task['team_away_name']; ?></p>
                                </span>
                            </div>
                            <div class="card-bottom">
                                <button class="predict-score" data-task-id="<?php echo $task['task_id']; ?>">Predict Score</button>
                            </div>
                    </li>
                    <?php endforeach; ?>
                </ul>
                <?php else: ?>
                <p class="no-match-live">No matches found.</p>
                <?php endif; ?>
            </div>
            <div class="tabs-content" id="content-select-2">
                <h1 class="tab-title">View the previous prediction results listed</h1>
                <h5 class="tab-head">Find your predicted score</h5>
                <div class="ongoing-matches">
                    <h4>Ongoing matches</h4>
                    <?php if (!empty($currentMatches)): ?>
                    <ul class="predict-list-current-matches">
                        <?php foreach ($currentMatches as $task): ?>
                        <li class="on-listing-matches">
                            <div class="card-top">
                                <div class="match-date-ongoing"><img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/dot.png" class="live-dot-icon" alt="Live Icon" style="vertical-align: middle;">Live</div>
                                <div class="locked"><img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/lock.png" class="lock-icon" alt="Lock Icon" style="vertical-align: middle;">Locked</div>
                            </div>
                            <div class="current-matches">
                                <span class="card-logo-name">
                                    <img src="<?php echo $task['team_home_logo']; ?>" alt="<?php echo $task['team_home_name']; ?>">
                                    <p class="team-name"><?php echo $task['team_home_name']; ?></p>
                                </span>
                                <div class="score-input">
                                    <button type="button" class="increment">+</button>
                                    <input type="number" name="home_team_score"
                                        value="<?php echo $task['predicted_home_team_score']; ?>" min="0">
                                    <button type="button" class="decrement">-</button>
                                </div>
                                <div class="score-input">
                                    <button type="button" class="increment">+</button>
                                    <input type="number" name="away_team_score"
                                        value="<?php echo $task['predicted_away_team_score']; ?>" min="0">
                                    <button type="button" class="decrement">-</button>
                                </div>
                                <span class="card-logo-name">
                                    <img src="<?php echo $task['team_away_logo']; ?>" alt="<?php echo $task['team_away_name']; ?>">
                                    <p class="team-name"><?php echo $task['team_away_name']; ?></p>
                                </span>
                            </div>
                            <div class="card-bottom">
                                <button class="predict-score-result">Predict Score</button>
                            </div>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php else: ?>
                    <p class="no-match-ongoing">No ongoing matches found for today.</p>
                    <?php endif; ?>
                </div>

                <div class="previous-predictions">
                    <h4>Previous predictions</h4>
                    <?php if (!empty($previousMatches)): ?>
                    <ul class="predict-list-user-matches">
                        <?php foreach ($previousMatches as $task): ?>
                        <li class="prev-listing-matches">
                            <div class="card-top">
                                <div class="match-date-previous"><img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/liveDot.png" class="live-dot-icon" alt="Live Icon" style="vertical-align: middle;"><?php echo $task['match_date']; ?></div>
                                <div class="rewards-earned"><img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/lixrewrd.png" class="reward-icon" alt="Reward Icon" style="vertical-align: middle;"><?php echo $task['reward_amount']." Lix Rewards"; ?></div>
                            </div>
                            <div class="match-teams">
                                <span class="card-logo-name">
                                    <img src="<?php echo $task['team_home_logo']; ?>" alt="<?php echo $task['team_home_name']; ?>">
                                    <p class="team-name"><?php echo $task['team_home_name']; ?></p>
                                </span>
                                <div class="team-predict-data">
                                    <div class="user-data-submit">
                                        <?php if ($task['reward_amount'] != 0) { ?>
                                        <h5 class="win">Win</h5>
                                        <?php } else { ?>
                                        <h5 class="try">Try Again</h5>
                                        <?php } ?>
                                        <div class="score-input">
                                            <!-- <button type="button" class="decrement">-</button> -->
                                            <input type="number" name="home_team_score" value="<?php echo $task['predicted_home_team_score']; ?>" min="0">
                                            <!-- <button type="button" class="increment">+</button> -->
                                            <input type="number" name="away_team_score" value="<?php echo $task['predicted_away_team_score']; ?>" min="0">
                                        </div>
                                    </div>
                                    <div class="user-data-predict">
                                        <h5 class="user-head">Final Score</h5>
                                        <div class="score-input">
                                            <!--<button type="button" class="decrement">-</button> -->
                                            <input type="number" name="home_team_score" value="<?php echo $task['finished_home_team_score']; ?>" min="0">
                                            <!--<button type="button" class="increment">+</button> -->
                                            <input type="number" name="away_team_score" value="<?php echo $task['finished_away_team_score']; ?>" min="0">
                                        </div>
                                    </div>
                                </div>
                                <span class="card-logo-name">
                                    <img src="<?php echo $task['team_away_logo']; ?>" alt="<?php echo $task['team_away_name']; ?>">
                                    <p class="team-name"><?php echo $task['team_away_name']; ?></p>
                                </span>
                            </div>
                            <?php if ($task['reward_amount'] != 0) { ?>
			    	<h5 class="congrats"><span class="card-bottom"><img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/medal-star.png" class="medal-star" alt="Medal Icon" style="vertical-align: middle;">Congratulation!</span> Your prediction was perfect!</h5>
			    <?php } else { ?>
			    	<h5 class="fail"><span class="card-bottom-fail">Failed!</span> Your prediction did not come as expected.</h5>
			    <?php } ?>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php else: ?>
                    <p class="no-match-previous">No previous matches found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
require(['jquery'], function($) {
    $(".tab-list li").on("click", function() {
        var tabId = ".tab-list li#" + $(this).attr("id");
        var tabDivId = ".tabs-content#content-" + $(this).attr("id");

        if (!$(this).hasClass("active")) {
            $(".tab-list li").removeClass("active");
            $(this).addClass("active");

            $(".tabs-content").removeClass("active");
            $(tabDivId).addClass("active");
        }
    });
    $(".predict li").on("click", function() {
        var tabId = ".predict li#" + $(this).attr("id");
        var tabDivId = ".predict-content#content-" + $(this).attr("id");

        if (!$(this).hasClass("active")) {
            $(".predict li").removeClass("active");
            $(this).addClass("active");

            $(".predict-content").removeClass("active");
            $(tabDivId).addClass("active");
        }
    });
});
</script>

<script>
require([
    'jquery',
    'mage/url',
    'Magento_Ui/js/modal/modal'
], function($, url, modal) {
    $(document).ready(function() {
        // Initialize the modal
        var options = {
            type: 'popup',
            title: 'Predict Score',
            buttons: [{
                text: 'Submit',
                class: 'action primary',
                click: function () {
                    var $form = $('#predict-form');
                    var taskId = $form.find('input[name="task_id"]').val();
                    var homeTeamScore = $form.find('input[name="home_team_score"]').val();
                    var awayTeamScore = $form.find('input[name="away_team_score"]').val();

                    var data = {
                        task_id: taskId,
                        home_team_score: homeTeamScore,
                        away_team_score: awayTeamScore
                    };

                    var submitUrl = url.build('lixreward/predict/submit');

                    $.ajax({
                        url: submitUrl,
                        type: 'POST',
                        data: data,
                        success: function(response) {
                            if (response && response.data && response.data.task_id) {
                                var taskId = response.data.task_id;
                                var $taskItem = $('li[data-task-id="' + taskId + '"]');
                                $taskItem.find('.predict-score').hide();
                                $taskItem.find('input[name="home_team_score"]').prop('disabled', true);
                                $taskItem.find('input[name="away_team_score"]').prop('disabled', true);
                                $taskItem.find('.decrement').prop('disabled', true);
                                $taskItem.find('.increment').prop('disabled', true);

                                $('.success-message').show().delay(10000).fadeOut();

                                // Store the disabled state in local storage
                                localStorage.setItem('task_' + taskId, true);
                                location.reload();
                            }
                        },
                        error: function(xhr, status, error) {
                            alert('An error occurred: ' + error);
                        }
                    });

                    this.closeModal();
                }
            }]
        };

        var popup = $('<div id="popup-modal"></div>').modal(options);

        $('.predict-list-matches').on('click', '.predict-score', function() {
            var $button = $(this);
            var $li = $button.closest('li');
            var taskId = $button.data('task-id');
            var homeTeamScore = $li.find('input[name="home_team_score"]').val();
            var awayTeamScore = $li.find('input[name="away_team_score"]').val();
            var homeTeamLogo = $li.find('img[alt="'+$li.find('.team-name:first').text()+'"]').attr('src');
            var awayTeamLogo = $li.find('img[alt="'+$li.find('.team-name:last').text()+'"]').attr('src');
            var homeTeamName = $li.find('.team-name:first').text();
            var awayTeamName = $li.find('.team-name:last').text();
            var matchDate = $li.find('.match-date').data('utc-time');
            var matchStatus = $li.find('.match-date').data('match-status');
            
            var formHtml = '<form id="predict-form">' +
                           '<input type="hidden" name="task_id" value="' + taskId + '">' +
                           '<div class="match-schedule">' + '</div>' +
                           '<div class="card-top">' +
                           '<div class="match-date" data-utc-time="' + matchDate + '" data-match-status="' + matchStatus + '"></div>' +
                           '<div class="no-prediction"></div>' +
                           '</div>' +
                           '<div class="match-teams">' +
                           '<span class="card-logo-name">' +
                           '<img src="' + homeTeamLogo + '" alt="' + homeTeamName + '">' +
                           '<p class="team-name">' + homeTeamName + '</p>' +
                           '</span>' +
                           '<div class="score-input">' +
                           '<button type="button" class="increment">+</button>' +
                           '<input type="text" name="home_team_score" value="' + homeTeamScore + '">' +
                           '<button type="button" class="decrement">-</button>' +
                           '</div>' +
                           '<div class="score-input">' +
                           '<button type="button" class="increment">+</button>' +
                           '<input type="text" name="away_team_score" value="' + awayTeamScore + '">' +
                           '<button type="button" class="decrement">-</button>' +
                           '</div>' +
                           '<span class="card-logo-name">' +
                           '<img src="' + awayTeamLogo + '" alt="' + awayTeamName + '">' +
                           '<p class="team-name">' + awayTeamName + '</p>' +
                           '</span>' +
                           '</div>' +
                           '</form>';

            $('#popup-modal').html(formHtml).modal('openModal');
        });
        
        // Event delegation for increment and decrement buttons in the modal
        $(document).on('click', '#popup-modal .increment', function() {
            var $input = $(this).siblings('input');
            $input.val(parseInt($input.val()) + 1);
        });

        $(document).on('click', '#popup-modal .decrement', function() {
            var $input = $(this).siblings('input');
            if (parseInt($input.val()) > 0) {
                $input.val(parseInt($input.val()) - 1);
            }
        });

        $('.predict-list-matches li').each(function() {
            var $li = $(this);
            var taskId = $li.data('task-id');
            if (localStorage.getItem('task_' + taskId)) {
                $li.find('.predict-score').hide();
                $li.find('input[name="home_team_score"]').prop('disabled', true);
                $li.find('input[name="away_team_score"]').prop('disabled', true);
                $li.find('.decrement').prop('disabled', true);
                $li.find('.increment').prop('disabled', true);
            }
        });
        
        // Disable buttons and inputs for tasks already predicted
        var predictedTasks = <?php echo json_encode(array_column($predictions, 'task_id')); ?>;
        $('.predict-list-matches li').each(function() {
            var $li = $(this);
            var taskId = $li.data('task-id');
            if (predictedTasks.includes(taskId)) {
                $li.find('.predict-score').hide();
                $li.find('input[name="home_team_score"]').prop('disabled', true);
                $li.find('input[name="away_team_score"]').prop('disabled', true);
                $li.find('.decrement').prop('disabled', true);
                $li.find('.increment').prop('disabled', true);
            }
        });

        // Disable all input fields and buttons inside the prediction lists
        $('.predict-list-user-matches').each(function() {
            $(this).find('input').attr('disabled', true);
            $(this).find('button').attr('disabled', true);
        });

        $('.predict-list-current-matches').each(function() {
            $(this).find('input').attr('disabled', true);
            $(this).find('button').attr('disabled', true);
        });

        function updateMatchDates() {
            document.querySelectorAll('.match-date').forEach(function(div) {
                var utcTime = div.getAttribute('data-utc-time');
                var matchStatus = div.getAttribute('data-match-status');
                // var predictDateScheduleLocalTime = div.closest('li').getAttribute('data-predict-date-schedule-local-time');
                var matchDate = new Date(utcTime);
                // var predictDate = new Date(predictDateScheduleLocalTime);
                var now = new Date();
                var utcNow = new Date(now.getTime() + now.getTimezoneOffset() * 60000);

                var options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' };
                var formattedUtcNow = utcNow.toLocaleDateString('en-US', options);

                document.querySelector('h3.utc-now').innerHTML = formattedUtcNow;

                var parentLi = div.closest('li');
                var noPredictionTextDiv = parentLi.querySelector('.no-prediction');
                var matchScheduleDiv = parentLi.querySelector('.match-schedule');
		// console.log(`Task ID: ${parentLi.getAttribute('data-task-id')}, UTC Now: ${utcNow}, Match Date: ${matchDate}`);
                if (utcNow >= matchDate) {
                    parentLi.querySelector('.predict-score').disabled = true;
                    parentLi.querySelectorAll('input[name="home_team_score"], input[name="away_team_score"]').forEach(function(input) {
                        input.disabled = true;
                    });
                    parentLi.querySelectorAll('.decrement, .increment').forEach(function(button) {
                        button.disabled = true;
                    });
                    parentLi.querySelectorAll('input, button').forEach(function(el) {
                        el.classList.add('disabled');
                    });
                    if (matchStatus === 'Finished') {
		        div.innerHTML = '<img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/dot.png" class="dot-live" alt="Live Icon" style="vertical-align: middle;">  Finished';
		        noPredictionTextDiv.innerHTML = '<img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/lock.png" class="lock-icon" alt="Lock Icon" style="vertical-align: middle;"> Locked';
		    } else {
		        div.innerHTML = '<img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/dot.png" class="dot-live" alt="Live Icon" style="vertical-align: middle;"> Live';
		        noPredictionTextDiv.innerHTML = '<img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/lock.png" class="lock-icon" alt="Lock Icon" style="vertical-align: middle;"> No Prediction Made';
		    }
                } else {
                    parentLi.querySelector('.predict-score').disabled = false;
                    parentLi.querySelectorAll('input[name="home_team_score"], input[name="away_team_score"]').forEach(function(input) {
                        input.disabled = false;
                    });
                    parentLi.querySelectorAll('.decrement, .increment').forEach(function(button) {
                        button.disabled = false;
                    });
                    parentLi.querySelectorAll('input, button').forEach(function(el) {
                        el.classList.remove('disabled');
                    });
                    div.textContent = '';

                    var formattedDate = matchDate.toLocaleString();
                    var iconImg = '<img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/liveDot.png" class="live-icon" alt="Live Icon" style="margin-left: 5px; vertical-align: middle;">';
                    matchScheduleDiv.innerHTML = iconImg + formattedDate;

                    var remainingTime = matchDate - utcNow;

                    var days = Math.floor(remainingTime / (1000 * 60 * 60 * 24));
                    var hours = Math.floor((remainingTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    var minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
                    var seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

                    var countdownText = '';
                    if (days > 0) {
                        countdownText += `${days}d `;
                    }
                    if (hours > 0) {
                        countdownText += `${hours}h `;
                    }
                    if (minutes > 0) {
                        countdownText += `${minutes}m `;
                    }
                    countdownText += `${seconds}s remaining`;

                    if (remainingTime > 0) {
                        noPredictionTextDiv.innerHTML = `<img src="<?php echo $mediaUrl; ?>wysiwyg/gamification/clock.png" class="clock-remaining" alt="Clock Icon" style="vertical-align: middle;"> ${countdownText}`;
                    } else {
                        noPredictionTextDiv.textContent = '';
                    }
                }
            });
        }

        updateMatchDates();
        setInterval(updateMatchDates, 1000);

        // Increment and decrement logic
        $('.increment').click(function() {
            var input = $(this).siblings('input');
            var value = input.val();
            if (value === '0') {
                input.val(1);
            } else {
                input.val(parseInt(value) + 1);
            }
        });

        $('.decrement').click(function() {
            var input = $(this).siblings('input');
            var value = input.val();
            if (value === '0') {
                input.val(0);
            } else if (parseInt(value) > 0) {
                input.val(parseInt(value) - 1);
            }
        });
    });
});
</script>

