<?php
$mediaUrl = $block->getMediaUrl();
$now = new \DateTime();

$offerblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Offers');

$pageValue = $this->getData('page');

$category = $offerblock->getCategory();

if (isset($category) && array_key_exists("success", $category) && $category['success'] == 1) {
    $categoryData = $category['data']['data'];
}
$country = $offerblock->getCountry();

if (isset($country) && $country['success'] == 1) {
    $countryData = $country['data']['data'];
}
$paramsData = $offerblock->getParmasDetail();
$keyword = $paramsData['search'] ?? '';
$category = $paramsData['categories'] ?? '';
$country = $paramsData['countries'] ?? '';
$orderby = $paramsData['orderby'] ?? '';

if (isset($details) && $details['success'] == 1) {
    $detailsData = $details['data'];
}
$isCustomerLoggedIn = $offerblock->isCustomerLoggedIn();
?>
    <div class="page-main">
        <div class="game-header">
            <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/copa-game.png'; ?>" alt="copa game">
            <h1>Play, Predict, Earn!</h1>
            <p>Test your COPA knowledge, predict match scores, and earn LIX rewards for amazing discounts and offers.
            </p>
            <!-- <button>Get Started</button> -->
        </div>
    </div>
    <section class="container-fluid">
        <div class="row">
            <div class="container tabs">
                <ul class="tab-list col-md-12">
                    <li id="select-1" class="active">
                        <i class="icon-gamepad"></i>
                        <h3>Games</h3>
                    </li>
                    <li id="select-2">
                        <i class="icon-gift"></i>
                        <h3>Rewards</h3>
                    </li>
                    <li id="select-3">
                        <i class="icon-line-help"></i>
                        <h3>Matches</h3>
                    </li>
                </ul>
            </div>
            <div class="container tab-content-one">
                <div class="tabs-content active" id="content-select-1">
                    <!-- Add code for upcoming games -->
                    <div class="games-dashboard">
                    <?php
                    
                    $matchDataList = $block->getMatcheslist();
                    $matchData = $matchDataList['todayMatches'];
                    $fixture = $matchDataList['fixtures'];
                    
                    ?>
                            <div class="game-container">
                                <h2>Upcoming Matches</h2>
                                <div class="match-list-container">
                                    <div class="matches-list row">
                                        <?php
                                if (!empty($matchData)) {
                                     
                                    // Initialize an array to store matches grouped by date
                                    $matchesByDate = [];

                                    // Group matches by date
                                    foreach ($matchData as $item) {
                                        $dateTime = new \DateTime($item['fixture']['date']);
                                        $matchDate = $dateTime->format('Y-m-d');


                                        $matchesByDate[$matchDate][] = $item;
                                    }

                                    foreach ($matchesByDate as $date => $matches) {
                                        // Calculate the difference between the match date and today
                                       
                                        $matchDateTime = new \DateTime($date);
                                        $interval = $now->diff($matchDateTime);
                                        $daysToGo = $interval->days;
                                        $daysText = $matchDateTime > $now ? "$daysToGo day" . ($daysToGo > 1 ? "s" : "") . " to go" : "Today";

                                        ?>
                                            <?php
                                        foreach ($matches as $item) {
                                            ?>
                                                <div class="matches col-lg-3 col-md-4 col-sm-6" id="match-details" style="background-color: white;">
                                                    <span class="match-date">
                                                    <span class="date-span">
                                                    <?php
                                                    if ($matchDateTime->format('Y-m-d') == $now->format('Y-m-d')) {
                                                        echo "Today";
                                                    } elseif ($matchDateTime->diff($now)->days == 0) {
                                                        echo "Tomorrow";
                                                    } else {
                                                        echo $daysText;
                                                    }
                                                    ?>
                                                    </span> at
                                                    <span data-timestamp="<?php echo $item['fixture']['timestamp']; ?>" class="match-time-span"></span>
                                                    </span>
                                                    <div class="match-teams">
                                                        <div class="home-team col-lg-5 col-md-5 col-sm-5">
                                                            <div class="home-team-img">
                                                                <img src="<?php echo $mediaUrl . 'wysiwyg/teamflag/' . $item['teams']['home']['name'] . '.png' ?>">
                                                            </div>
                                                            <span>
                                                            <?php echo $item['teams']['home']['name']; ?>
                                                        </span>
                                                        </div>
                                                        <div class="match-time col-lg-2 col-md-2 col-sm-2">
                                                            <span class="vs">V/S</span>
                                                        </div>
                                                        <div class="away-team col-lg-5 col-md-5 col-sm-5">
                                                            <div class="away-team-img">
                                                                <img src="<?php echo $mediaUrl . 'wysiwyg/teamflag/' . $item['teams']['away']['name'] . '.png' ?>">
                                                            </div>
                                                            <span>
                                                            <?php echo $item['teams']['away']['name']; ?>
                                                        </span>
                                                        </div>
                                                        <!-- <div class="match-venue">
                                                    <div class="description"><?php echo $item['fixture']['venue']['name'] . ", " . $item['fixture']['venue']['city']; ?></div>
                                                </div> -->
                                                    </div>
                                                </div>
                                                <?php
                                        }
                                    }
                                } else {
                                    echo "<p>No matches available</p>";
                                }
                                ?>
                                    </div>
                                </div>

                            </div>
                    </div>
                    <div class="play-earn">
                        <h1 class="game-heading-text">Play to Earn</h1>
                        <div class="game-list-predict">
                            <div class="game-predict">
                                <a href="<?= $this->getUrl('lixreward/predict/view') ?>">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/predict-scorenew.png'; ?>" alt="">
                                <h3>Predict Score</h3>
                            </a>
                            </div>
                            <div class="game-predict">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/firstgoal-new.png'; ?>" alt="">
                                <h3>First Goal Scorer</h3>
                            </div>
                            <div class="game-predict">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/pollparty-new.png'; ?>" alt="">
                                <h3>Poll Party</h3>
                            </div>
                            <div class="game-predict">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/predictsoon.png'; ?>" alt="">
                                <h3>Predict Lineup</h3>
                            </div>

                        </div>
                    </div>
                    <div class="play-game">
                        <h1 class="game-heading-text">Earn Daily</h1>
                        <div class="game-list">
                            <div id="game-play-spin" class="game-play">
                                <form action="<?php echo $block->getUrl('lixreward/gamification/playnow'); ?>" method="post">
                                    <input type="hidden" name="id" id="spin-id">
                                    <input type="hidden" name="task_type" id="spintype">
                                    <div class="unlock-game" id="countdown"></div>
                                    <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/Spin-wheel.png'?>" alt="spin the wheel">
                                    <div class="game-content">
                                        <input type="hidden" id="scheduleTime" name="scheduleTime" readonly>
                                        <h3>Spin the Wheel</h3>
                                        <button class="spinwheel" type="submit">Play Now</button>
                                    </div>
                                </form>

                            </div>
                            <div id="game-play-scratch" class="game-play">
                                <form action="<?php echo $block->getUrl('lixreward/gamification/playnow'); ?>" method="post">
                                    <input type="hidden" name="id" id="scracth-id">
                                    <input type="hidden" name="task_type" id="scratchtype">
                                    <div class="unlock-game" id="scratchCardTimer"></div>
                                    <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/scratch-earn.png'; ?>" alt="spin the wheel">
                                    <div class="game-content">
                                        <input type="hidden" id="scratchCard" name="scratchCard" readonly>
                                        <h3>Scratch the Card</h3>
                                        <button class="scratchcard" type="submit">Play Now</button>
                                    </div>
                                </form>
                            </div>
                            <div id="game-play-quiz" class="game-play">
                                <form action="<?php echo $block->getUrl('lixreward/gamification/playnow'); ?>" method="post">
                                    <input type="hidden" name="id" id="quiz-id">
                                    <input type="hidden" name="task_type" id="quiztype">
                                    <div class="unlock-game" id="quizTimer"></div>
                                    <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/daily-quiz.png'; ?>" alt="spin the wheel">
                                    <div class="game-content">
                                        <input type="hidden" id="quiz" name="quiz" readonly>
                                        <h3>The Quiz</h3>
                                        <button class="dailyquiz" type="submit">Play Now</button>
                                    </div>
                                </form>
                            </div>

                            <div class="game-play">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/treasurehunt.png'; ?>">
                                <div class="game-content">
                                    <h3>AR Treasure Hunt</h3>
                                    <button class="disable-game">Play Now</button>
                                </div>
                            </div>
                            <div class="game-play">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/spotball.png'; ?>">
                                <div class="game-content">
                                    <h3>Spot the  Ball</h3>
                                    <button class="disable-game">Play Now</button>
                                </div>
                            </div>
                            <div class="game-play">
                                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/wordscrambler.png'; ?>">
                                <div class="game-content">
                                    <h3>Word Scrambler</h3>
                                    <button class="disable-game">Play Now</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tabs-content bar-chart" id="content-select-2">
                    <?php if ($isCustomerLoggedIn): ?>
                        <?php
                        $_helper = $this->helper(\Comave\LixApiConnector\Helper\Data::class);
                        $blockName = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');
                        $custEmail = $blockName->getCustomerEmail();
                        $custWallet = $blockName->getLixWallet();
                        $lixWallet = 'lix_wallet';

                        if ($custWallet == 'LIXX') {
                            $currconv = number_format((float) $_helper->getLixCurrenciesValues($custEmail, $lixWallet), 4, '.', '');
                        } else {
                            $currconv = number_format((float) $_helper->getLixCurrenciesValues($custEmail, $lixWallet), 2, '.', '');
                        }
                        if ($blockName->getRewardPoint() >= 1) {
                            $reward = number_format((float) $blockName->getRewardPoint(), 2, '.', '');
                            $rreward = ceil($reward);
                        } else {
                            $reward = number_format((float) $blockName->getRewardPoint(), 3, '.', '');
                            $rreward = ceil($reward);
                        }

                        if ($blockName->getPoints() >= 1) {
                            $currencyBalance = number_format((float) $blockName->getPoints(), 2, '.', '');
                        } else {
                            $currencyBalance = number_format((float) $blockName->getPoints(), 3, '.', '');
                        }
                        $couponblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Coupon');
                        $couponlist = $couponblock->getCouponlistDetails();

                        $walletblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\WalletData');
                        $walletBalance = $walletblock->getWalletBalance();

                        if (isset($details) && $details['success'] == 1) {
                            $detailsData = $details['data'];
                        }
                        $count = 0;
                        if (isset($couponlist['success']) && $couponlist['success'] == 1) {
                            $couponcountData = $couponlist['data']['data'];
                            foreach ($couponcountData as $itemcount) {
                                $count++;
                            }
                        }
                    ?>
                            <div class="page-main-lix">
                                <h4 class="page-title">
                            <?= $block->escapeHtml(__('my account')) ?>
                        </h4>
                                <div class="main-section-top">
                                    <div class="section-main-1">
                                        <div class="main-inner">
                                            <h4 class="lix-heading">LIX Rewards</h4>
                                            <span class="lix-tocken">
                                        <?= $block->escapeHtml($walletBalance['points']) ?>
                                    </span>
                                            <span class="lix-currency">$
                                        <?= $block->escapeHtml($walletBalance['currency_amount']) ?>
                                    </span>
                                            <span class="lix-conversion">
                                        <?php if (isset($custWallet)) { ?>
                                            <p>
                                                <?= $block->escapeHtml(__('1 LIX Rewards = $')) ?>
                                                <?= $block->escapeHtml($currconv) ?>
                                            </p>
                                        <?php } ?>
                                    </span>
                                        </div>
                                        <div>
                                            <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/lix-dashboard-img/lix-img-logo.png'; ?>" alt="lix-reward">
                                        </div>
                                    </div>
                                    <div class="section-main-2">
                                        <div class="section-two-inner">
                                            <div class="section-inner-sec">
                                                <h4>
                                            <?= $block->escapeHtml(__('Earn Rewards')) ?>
                                        </h4>
                                                <span>
                                            <?= $block->escapeHtml(__('Unlock rewards effortlessly! Simply complete listed tasks and scan to earn points. Start earning today!')) ?>
                                        </span>
                                            </div>
                                            <div class="inner-sec-two">
                                                <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/lix-dashboard-img/earn-logo.png'; ?>" alt="earn-reward">
                                            </div>
                                        </div>
                                        <div class="section-buttons">
                                            <a href="<?= $this->getUrl('lixreward/EarnReward/earn/') ?>" class="earn-reward">
                                                <button class="earn-primary">
                                                    <?= $block->escapeHtml(__('Earn Rewards')) ?>
                                                </button>
                                            </a>
                                            <!-- <a href="#" class="scan-earn">
                                            <button id="scan-and-earn-btn" class="scan-secondary"><//?= $block->escapeHtml(__('Scan and Earn')) ?></button>
                                        </a> -->
                                            <!-- <div id="scanner" style="width: 100%; height: 300px;"></div> -->

                                            <!-- HTML -->
                                            <button onclick="openScannerModal()">Open QR Code Scanner</button>
                                            <div id="scannerModal" class="modal" style="display:none;">
                                                <div class="modal-content">
                                                    <span class="close" onclick="closeScannerModal()">&times;</span>
                                                    <div id="reader" style="width: 100%; height: 300px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="section-main-3">
                                        <div class="section-two-inner">
                                            <div class="sec-three">
                                                <h4>
                                                <?= $block->escapeHtml(__('Redeem Rewards')) ?>
                                            </h4>
                                                <span>
                                                <?= $block->escapeHtml(__('Redeem your rewards with exclusive offers. Treat yourself with just a click!')) ?>
                                            </span>
                                            </div>
                                            <div class="lix-card"><img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/lix-dashboard-img/redeem-card.png'; ?>" alt="lix-card"></div>
                                        </div>
                                        <div class="redeem-buttons">
                                            <a href="<?= $this->getUrl('lixreward/lixpay/wallet/') ?>" class="lixpay">
                                                <button class="earn-primary"><img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/lix-dashboard-img/lix-logo.png'; ?>" alt="lixpay">
                                                    <?= $block->escapeHtml(__('Pay')) ?>
                                                </button>
                                            </a>
                                            <a href="<?= $this->getUrl('lixreward/marketplace/offers/') ?>" class="marketplace">
                                                <button class="scan-secondary">
                                                    <?= $block->escapeHtml(__('Offers')) ?>
                                                </button>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="section-main-4">
                                        <h4 class="section-heading">
                                        <?= $block->escapeHtml(__('my wallets')) ?>
                                    </h4>
                                        <div class="section-4-lix">
                                            <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/lix-dashboard-img/lix-brand-logo.png'; ?>" alt="wallet">
                                            <div class="sec-four">
                                                <h4 class="wallet-heading">
                                                <?php if ($walletBalance['currency'] !== null) { ?>
                                                    <?= $block->escapeHtml($walletBalance['currency']) ?>

                                                </h4>
                                                <h4>
                                                    <?= $block->escapeHtml($walletBalance['points']) ?> 
                                                    <!-- <//?= $block->escapeHtml($walletBalance['currency_amount']); ?> -->
                                                </h4>

                                                <?php } ?>
                                            </div>
                                        </div>
                                        <!-- <a href="#" class="wallet"> 
                                            <button class="see-all-btn"><?= $block->escapeHtml(__('see all')) ?></button>
                                        </a> -->
                                    </div>
                                </div>
                            </div>
                            <section class="container-fluid">
                                <div class="row">
                                    <div class="container tabs">
                                        <ul class="reward-list col-md-12">
                                            <li id="select-inner-3" class="active">
                                                <h3>
                                            <?= $block->escapeHtml(__('Coupons')) ?>
                                        </h3>
                                                <span class="coupon-count">
                                            <?= $block->escapeHtml($count) ?>
                                        </span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="container tab-content-one-inner">

                                        <div class="tabs-content-inner coupon" id="content-select-inner-3">
                                            <div class="tab-header col-md-12 text-center">
                                                <div class="coupons-top">
                                                    <?php
                                            if (isset($couponlist['success']) && $couponlist['success'] == 1) {
                                                ?>
                                                        <a href="<?= $this->getUrl('lixreward/coupon/mycoupon/') ?>" class="coupons-btn">
                                                            <button class="coupons-see-all-btn">
                                                                <?= $block->escapeHtml(__('see all')) ?>
                                                            </button>
                                                        </a>
                                                        <?php
                                            }
                                            ?>
                                                </div>
                                                <div class="coupon-list-container">
                                                    <div class="coupons-list row">
                                                        <?php
                                                if (isset($couponlist['success']) && $couponlist['success'] == 1) {
                                                    $couponData = $couponlist['data']['data'];
                                                    foreach ($couponData as $item) {
                                                        ?>
                                                            <div class="coupons col-lg-3 col-md-4 col-sm-6 mb-3" id="coupon-details">
                                                                <div class="coupon-grid">
                                                                    <div class="offer-img-container">
                                                                        <img src="<?php echo $item['market']['offer_image']; ?>">
                                                                    </div>
                                                                    <div class="description">
                                                                        <?php echo $item['description']; ?>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <?php
                                                        $couponDetails = $couponblock->getCouponDetails($item['id']);
                                                        ?>
                                                                <!-- Coupon container -->
                                                                <?php
                                                        if (isset($couponDetails['success']) && $couponDetails['success'] == 1 && isset($couponDetails['data'])) {
                                                            ?>
                                                                    <div class="coupon-details-popup-container">
                                                                        <div class="coupon-details-container" id="coupon-details-popup">
                                                                            <span class="coupon-details-popup-close">&times;</span>
                                                                            <div class="coupon-body row">
                                                                                <div class="coupon-desc">
                                                                                    <?php echo $couponDetails['data']['description']; ?>
                                                                                </div>
                                                                                <div class="coupon-url">
                                                                                    <div class="coupon-url-link">
                                                                                        <a id="couponLink" href="<?php echo $couponDetails['data']['coupon']; ?>" target="_blank">
                                                                                            <?php echo $couponDetails['data']['coupon']; ?>
                                                                                        </a>
                                                                                    </div>
                                                                                    <div class="copy-button">
                                                                                        <button class="copy-coupon-url" onclick="copyCouponLink('<?php echo $couponDetails['data']['coupon']; ?>')"><i class="icon-line-stack-2"></i></button>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="show-qrcode">
                                                                                    <button class="coupon-qrcode">
                                                                                        <?= $block->escapeHtml(__('Show QR Code')) ?>
                                                                                    </button>
                                                                                    <button class="hide-coupon-qrcode" style="display: none;">
                                                                                        <?= $block->escapeHtml(__('Hide QR Code')) ?>
                                                                                    </button>
                                                                                    <div>
                                                                                        <?php
                                                                                $qr = "https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=" . $couponDetails['data']['coupon'];
                                                                                ?>
                                                                                            <img class="coupon-qrcode-img" src="<?php echo $qr; ?>" alt="QR Code" style="display: none;">
                                                                                    </div>
                                                                                </div>
                                                                                <div class="show-barcode">
                                                                                    <button class="coupon-barcode">
                                                                                        <?= $block->escapeHtml(__('Show Barcode')) ?>
                                                                                    </button>
                                                                                    <button class="hide-coupon-barcode" style="display: none;">
                                                                                        <?= $block->escapeHtml(__('Hide Barcode')) ?>
                                                                                    </button>
                                                                                    <div>
                                                                                        <img class="coupon-barcode-img" src="data:image/png;base64,<?php echo $couponDetails['data']['coupon_barcode']; ?>" alt="Barcode" style="display: none;">
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <?php
                                                        }
                                                    }
                                                } else {
                                                    ?>
                                                                        <div class="message info empty">
                                                                            <span>
                                                            <?= $block->escapeHtml(__('No coupons available.')) ?>
                                                        </span>
                                                                        </div>
                                                                        <?php
                                                }
                                                ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                            <div class="offers-list">
                                <div class="sort-section">
                                    <div class="filter-offer row">
                                        <form id="filterForm" method="get">
                                            <div class="category col-lg-4 col-md-4 col-sm-6">
                                                <select name="categories" id="categorySelect">
                                                    <option value="">
                                                        <?= $block->escapeHtml(__('Category')) ?>
                                                    </option>
                                                    <?php
                                            if (isset($categoryData)) {
                                                foreach ($categoryData as $categories) {
                                                    ?>
                                                     <option value="<?php echo $categories['name']; ?>" <?php if ($category==$categories[ 'name']) { echo "selected"; } ?>>
                                                            <?php echo ucwords($categories['name']); ?>
                                                        </option>
                                                        <?php
                                                }
                                            } else {
                                                ?>
                                                            <option value="">
                                                                <?php echo $block->escapeHtml(__('No categories available')); ?>
                                                            </option>
                                                            <?php
                                            }
                                            ?>
                                                </select>
                                            </div>
                                            <div class="country col-lg-4 col-md-4 col-sm-6">
                                                <select name="countries" id="countrySelect">
                                                    <option value="">
                                                        <?= $block->escapeHtml(__('Country')) ?>
                                                    </option>
                                                    <?php
                                            if (isset($countryData)) {
                                                foreach ($countryData as $countries) {
                                                    ?>
                                                        <option value="<?php echo $countries['id']; ?>" <?php if ($country==$countries[ 'id']) { echo "selected"; } ?>>
                                                            <?php echo ucwords($countries['name']); ?>
                                                        </option>
                                                        <?php
                                                }
                                            } else {
                                                ?>
                                                            <option value="">
                                                                <?php echo $block->escapeHtml(__('No countries available')); ?>
                                                            </option>
                                                            <?php
                                            }
                                            ?>
                                                </select>
                                            </div>
                                            <div class="order-by col-lg-4 col-md-4 col-sm-6">
                                                <select name="orderby" id="orderbySelect">
                                                    <option value="">
                                                        <?= $block->escapeHtml(__('Order By')) ?>
                                                    </option>
                                                    <option value="asc" <?php if ($orderby=="asc" ) { echo "selected"; } ?>>
                                                        <?= $block->escapeHtml(__('Name A to Z')) ?>
                                                    </option>
                                                    <option value="desc" <?php if ($orderby=="desc" ) { echo "selected"; } ?>>
                                                        <?= $block->escapeHtml(__('Name Z to A')) ?>
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="filter-actions">
                                                <button type="submit" title="Search" class="action filter-search" aria-label="Search">
                                                    <span class="offer-search"></span>
                                                </button>
                                            </div>
                                            <button type="button" class="action clear-filters" aria-label="Clear Filters">
                                                <?= $block->escapeHtml(__('Clear Filters')) ?>
                                            </button>
                                        </form>
                                    </div>
                                    <div class="search-offer row">
                                        <div class="keyword col-lg-12 col-md-12 col-sm-12">
                                            <form class="keyword" method="get">
                                                <input type="text" name="search" placeholder="Search" id="searchInput" value="<?php if ($keyword) {
                                            echo $keyword;
                                        } ?>">
                                                <span class="cancel-button" id="cancelButton">&times;</span>
                                                <div class="actions">
                                                    <button type="submit" title="Search" class="action search" aria-label="Search">
                                                        <span class="offer-search">search</span>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <?php else: ?>
                                <p class="guest-reward-msg">
                                    <?= $block->escapeHtml(__('Please')) ?>
                                        <a href="<?= $block->getUrl('customer/account/login') ?>">
                                            <?= $block->escapeHtml(__('log in')) ?>
                                        </a>
                                        <?= $block->escapeHtml(__('to see rewards.')) ?>
                                </p>
                                <?php endif; ?>

                </div>

                <div class="tabs-content" id="content-select-3">
                    <div class="matches-container">
                        <div class="dates-container" id="match-dates"></div>
                        <div class="match-list-container">
                            <div class="matches-list row" id="matches-list"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="game-sectionone">
                <p>Play games to start earning LIX Rewards and many more</p>
                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/lixgame1.png'; ?>" alt="">
            </div>
            <div class="game-sectiontwo">
                <img src="<?php echo $mediaUrl . 'wysiwyg/gamification/lixgame2.png'; ?>" alt="">
                <p>Use it to purchase from our shop to get a discount</p>
            </div>
        </div>
    </section>


    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            var timeElements = document.querySelectorAll('.match-time-span');
            timeElements.forEach(function (element) {
                var timestamp = parseInt(element.getAttribute('data-timestamp'), 10);
                var date = new Date(timestamp * 1000);
                var options = {
                    timeZone: timezone,
                    hour: 'numeric',
                    minute: 'numeric',
                    hour12: true
                };
    
                var localDateTime = new Intl.DateTimeFormat([], options).format(date);
                element.textContent = localDateTime;
            });
        });
    </script>
    <script>
        function copyCouponLink(couponUrl) {
            var tempInput = document.createElement("input");
            tempInput.value = couponUrl;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand("copy");
            document.body.removeChild(tempInput);
        }
     
        require(['jquery', 'slick'], function ($) {
    
            $(document).ready(function () {
                let promotionSlider = $('.offers.grid.items.offers-items.slick-carousel');
                if (promotionSlider.length) {
    
                    promotionSlider.slick({
                        slidesToShow: 4,
                        slidesToScroll: 1,
                        autoplay: true,
                        autoplaySpeed: 5000,
                        arrows: true,
                        dots: false,
                        responsive: [{
                            breakpoint: 1023,
                            settings: {
    
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 3,
                            }
                        },
                        {
                            breakpoint: 768,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 2
                            }
                        },
                        {
                            breakpoint: 576,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 1
                            }
                        },
                        {
                            breakpoint: 480,
                            settings: {
                                arrows: true,
                                slidesToShow: 1
                            }
                        }
                        ],
                    });
                }
    
    
            });
    
            $(".tab-list li").on("click", function () {
                var tabId = ".tab-list li#" + $(this).attr("id");
                var tabDivId = ".tabs-content#content-" + $(this).attr("id");
    
                if (!$(this).hasClass("active")) {
                    $(".tab-list li").removeClass("active");
                    $(this).addClass("active");
    
                    $(".tabs-content").removeClass("active");
                    $(tabDivId).addClass("active");
                }
            });
            $(".lix-history li").on("click", function () {
                var tabId = ".lix-history li#" + $(this).attr("id");
                var tabDivId = ".lix-history-content#content-" + $(this).attr("id");
    
                if (!$(this).hasClass("active")) {
                    $(".lix-history li").removeClass("active");
                    $(this).addClass("active");
    
                    $(".lix-history-content").removeClass("active");
    
                    $(tabDivId).addClass("active");
                }
            })
    
            $(".reward-list  li").on("click", function () {
                var tabId = ".reward-list li#" + $(this).attr("id");
                var tabDivId = ".tabs-content-inner#content-" + $(this).attr("id");
    
                if (!$(this).hasClass("active")) {
                    $(".reward-list li").removeClass("active");
                    $(this).addClass("active");
    
                    $(".tabs-content-inner").removeClass("active");
    
                    $(tabDivId).addClass("active");
                }
            })
            $(document).ready(function () {
                let gameSlider = $('.games-dashboard .matches-list.row');
                if (gameSlider.length) {
                    gameSlider.slick({
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        autoplay: true,
                        autoplaySpeed: 5000,
                        arrows: true,
                        dots: false,
                        responsive: [{
                            breakpoint: 1023,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 2,
                            }
                        },
                        {
                            breakpoint: 768,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 2
                            }
                        },
                        {
                            breakpoint: 576,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 1
                            }
                        },
                        {
                            breakpoint: 480,
                            settings: {
                                arrows: true,
                                slidesToShow: 1
                            }
                        }
                        ],
                    });
                }
            });
        });
        require(['jquery'], function ($) {
            $(document).ready(function () {
                $('.coupons').each(function (index) {
                    var details = $(this);
                    details.click(function (e) {
                        e.preventDefault();
                        var newClass = 'mycoupon-preview';
                        document.body.classList.add(newClass);
                        details.next('.coupon-details-popup-container').show();
                    });
                });
                $('.coupon-details-popup-close').click(function (e) {
                    e.preventDefault();
                    var newClass = 'mycoupon-preview';
                    if (document.body.classList.contains(newClass)) {
                        document.body.classList.remove(newClass);
                    }
                    var container = $(this).closest('.coupon-details-popup-container');
                    container.hide();
                    container.find('.coupon-qrcode-img').hide();
                    container.find('.coupon-barcode-img').hide();
                    container.find('.coupon-qrcode').show();
                    container.find('.hide-coupon-qrcode').hide();
                    container.find('.coupon-barcode').show();
                    container.find('.hide-coupon-barcode').hide();
                });
    
                $(document).on('click', '.coupon-qrcode', function () {
                    var qrCodeImg = $(this).closest('.coupon-body').find('.coupon-qrcode-img');
                    qrCodeImg.css('display', 'inline-block');
                    $(this).siblings('.hide-coupon-qrcode').show();
                    $(this).hide();
                });
    
                $(document).on('click', '.hide-coupon-qrcode', function () {
                    var qrCodeImg = $(this).closest('.coupon-body').find('.coupon-qrcode-img');
                    qrCodeImg.hide();
                    $(this).siblings('.coupon-qrcode').show();
                    $(this).hide();
                });
    
                $(document).on('click', '.coupon-barcode', function () {
                    var barcodeImg = $(this).closest('.coupon-body').find('.coupon-barcode-img');
                    barcodeImg.css('display', 'inline-block');
                    $(this).siblings('.hide-coupon-barcode').show();
                    $(this).hide();
                });
    
                $(document).on('click', '.hide-coupon-barcode', function () {
                    var barcodeImg = $(this).closest('.coupon-body').find('.coupon-barcode-img');
                    barcodeImg.hide();
                    $(this).siblings('.coupon-barcode').show();
                    $(this).hide();
                });
            });
        });
    </script>
    <!-- Pranali script -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var moreButtons = document.querySelectorAll('.currencies-popup');
            moreButtons.forEach(function (moreButton) {
                moreButton.addEventListener('click', function (event) {
                    event.stopPropagation();
                    var popup = this.nextElementSibling;
                    document.querySelectorAll('.popup.show').forEach(function (openPopup) {
                        if (openPopup !== popup) {
                            openPopup.classList.remove('show');
                        }
                    });
                    popup.classList.toggle('show');
                });
            });
    
            var closeButtons = document.querySelectorAll('.close-btn');
            closeButtons.forEach(function (closeButton) {
                closeButton.addEventListener('click', function () {
                    var popup = this.closest('.popup');
                    popup.classList.remove('show');
                });
            });
    
            document.addEventListener('click', function (e) {
                var openPopups = document.querySelectorAll('.popup.show');
                openPopups.forEach(function (popup) {
                    if (!popup.contains(e.target)) {
                        popup.classList.remove('show');
                    }
                });
            });
            var categorySelect = document.getElementById('categorySelect');
            var countrySelect = document.getElementById('countrySelect');
            var orderbySelect = document.getElementById('orderbySelect');
            if (categorySelect.length === 0) {
                categorySelect.addEventListener('change', function () {
                    document.getElementById('filterForm').submit();
                });
            }
            if (countrySelect.length === 0) {
                countrySelect.addEventListener('change', function () {
                    document.getElementById('filterForm').submit();
                });
            }
            if (orderbySelect.length === 0) {
                orderbySelect.addEventListener('change', function () {
                    document.getElementById('filterForm').submit();
                });
            }
            var clearFiltersButton = document.querySelector('.clear-filters');
            clearFiltersButton.addEventListener('click', function () {
                var url = window.location.origin + window.location.pathname;
                window.location.href = url;
            });
        
            const searchInput = document.getElementById('searchInput');
            const cancelButton = document.getElementById('cancelButton');
            if (searchInput.value.trim() !== '') {
                cancelButton.style.display = 'inline-block';
            }
            searchInput.addEventListener('input', function (event) {
                if (event.target.value.trim() !== '') {
                    cancelButton.style.display = 'inline-block';
                } else {
                    cancelButton.style.display = 'none';
                }
            });
            cancelButton.addEventListener('click', function () {
                var url = window.location.origin + window.location.pathname;
                window.location.href = url;
            });
        });
    </script>

    <script>
        //new script for drag
         require(['jquery'], function ($) {
            $(document).ready(function () {
              const $dateList = $('#match-dates');
              let isDown = false;
              let startX;
              let scrollLeft;
    
              // Mouse events
              $dateList.on('mousedown', function (e) {
                isDown = true;
                startX = e.pageX - $dateList.offset().left;
                scrollLeft = $dateList.scrollLeft();
                $dateList.css('cursor', 'grabbing');
              });
    
              $dateList.on('mouseleave mouseup', function () {
                isDown = false;
                $dateList.css('cursor', 'grab');
              });
    
              $dateList.on('mousemove', function (e) {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - $dateList.offset().left;
                const walk = (x - startX) * 2; //scroll-fast
                $dateList.scrollLeft(scrollLeft - walk);
              });
    
              // Touch events
              $dateList.on('touchstart', function (e) {
                isDown = true;
                startX = e.originalEvent.touches[0].pageX - $dateList.offset().left;
                scrollLeft = $dateList.scrollLeft();
              });
    
              $dateList.on('touchend', function () {
                isDown = false;
              });
    
              $dateList.on('touchmove', function (e) {
                if (!isDown) return;
                e.preventDefault();
                const x = e.originalEvent.touches[0].pageX - $dateList.offset().left;
                const walk = (x - startX) * 2; //scroll-fast
                $dateList.scrollLeft(scrollLeft - walk);
              });
            });
          });
    </script>
    <script>
        require(['jquery'], function ($) {
            $(document).ready(function () {
            async function fetchMatches() {
            try {
                const response = await $.ajax({
                    url: '/lixreward/gamification/matches',
                    method: 'GET',
                    dataType: 'json'
                });
    
                if (response) {
                    displayMatches(response);
                } else {
                    $('#match-dates').html("<p>No matches available</p>");
                    $('#matches-list').html("<p>No matches available</p>");
                }
            } catch (error) {
                console.error('Error fetching matches:', error);
                $('#match-dates').html("<p>Failed to load matches</p>");
                $('#matches-list').html("<p>Failed to load matches</p>");
            }
        }
    
        function displayMatches(fixtures) {
            let datesHtml = '';
            let matchesHtml = '';
            var mediaurll = '<?php echo $mediaUrl;?>wysiwyg/teamflag/';
            for (const [date, matches] of Object.entries(fixtures)) {
                const dateTime = new Date(date);
                const formattedDate = dateTime.toLocaleDateString('en-US', { weekday: 'long', day: '2-digit', month: 'long' });
    
                datesHtml += `<div class="match-row">
                                <a href="#${formattedDate}">
                                    <h2>${formattedDate}</h2>
                                </a>
                              </div>`;
    
                matchesHtml += `<div class="match-row">
                                <h2 id="${formattedDate}">${formattedDate}</h2>`;
    
                matches.forEach(match => {
                    matchesHtml += `<div class="matches col-lg-3 col-md-4 col-sm-6" id="match-details">
                                    <div class="match-teams">
                                        <div class="home-team col-lg-5 col-md-5 col-sm-5">
                                            <div class="home-team-img">
                                                
                                                <img src="${mediaurll}${match.home_team}.png"> 
                                            </div>
                                            <span>${match.home_team}</span>
                                        </div>
                                        <div class="match-time col-lg-2 col-md-2 col-sm-2">
                                            <span data-timestamp="${match.timestamp}" class="match-time-span"></span>
                                        </div>
                                        <div class="away-team col-lg-5 col-md-5 col-sm-5">
                                            <div class="away-team-img">
                                               
                                        <img src="${mediaurll}${match.away_team}.png"> 
                                                
                                            </div>
                                            <span>${match.away_team}</span>
                                        </div>
                                    </div>
                                </div>`;
                });
    
                matchesHtml += `</div>`;
            }
    
            $('#match-dates').html(datesHtml);
            $('#matches-list').html(matchesHtml);
        }
                fetchMatches();
            });
        });
    </script>

    <script type="text/javascript">
        require(['jquery'], function ($) {
        
        $(document).ready(function() {
           
            var spinplay = '<?php echo $block->getUrl('lixreward/gamification/playnow'); ?>';
            var mediaurl = '<?php echo $mediaUrl;?>';
            $('.spinwheel').prop('disabled', true);
            $('.dailyquiz').prop('disabled', true);
            $('.scratchcard').prop('disabled', true);
            // AJAX call for Spin Wheel
            $.ajax({
                url: '/lixreward/gamification/getspinwheel',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    if (response.spinWheelList) {
                        updateSpinWheel(response.spinWheelList, spinplay, mediaurl);
                    }
                },
                error: function() {
                    console.error('An error occurred while fetching the spin wheel list.');
                }
            });
    
            // AJAX call for Scratch Card
            $.ajax({
                url: '/lixreward/gamification/getscratchcard',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    if (response.scratchCardList) {
                       
                        updateScratchCard(response.scratchCardList, spinplay, mediaurl);
                    }else {
                        updateScratchCard(null, spinplay, mediaurl); // Call with null to handle empty case
                    }
                },
                error: function() {
                    console.error('An error occurred while fetching the scratch card list.');
                }
            });
       
            // AJAX call for Quiz
                $.ajax({
                    url: '/lixreward/gamification/getquiz',
                    method: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        console.log(response);
                        if (response.quizList) {
                           
                            updateQuiz(response.quizList, spinplay, mediaurl);
                        }else {
                             
                        updateQuiz(null, spinplay, mediaurl); // Call with null to handle empty case
                    }
                    },
                    error: function() {
                        console.error('An error occurred while fetching the quiz list.');
                    }
                });
            
            function updateSpinWheel(spinWheelList, spinplay, mediaurl) {
                if (!$.isEmptyObject(spinWheelList)) {
                      
                    $('#spin-id').attr('value',spinWheelList.id);
                    $('#spintype').attr('value',spinWheelList.task_type);
                    $('#scheduleTime').attr('value',spinWheelList.schedule_utc_time);
                    $('#scheduleTime').attr('statustype',spinWheelList.taskstatus);
                    
                } 
                
                gamesTimecount();
            }
        
            function updateScratchCard(scratchCardList, spinplay, mediaurl) {
                if (scratchCardList && !$.isEmptyObject(scratchCardList)) {
                
                    $('#scracth-id').attr('value',scratchCardList.id);
                    $('#scratchtype').attr('value',scratchCardList.task_type);
                    $('#scratchCard').attr('value',scratchCardList.schedule_utc_time);
                    $('#scratchCard').attr('statustype',scratchCardList.taskstatus);
                    

                    
                }
                gamesTimecount();
        
            }
        
            function updateQuiz(quizList, spinplay, mediaurl) {
                if (quizList && !$.isEmptyObject(quizList)) {
                    $('#quiz-id').attr('value',quizList.id);
                    $('#quiztype').attr('value',quizList.task_type);
                    $('#quiz').attr('value',quizList.schedule_utc_time);
                    $('#quiz').attr('statustype',quizList.taskstatus);
                    
                }
                gamesTimecount();
            }
            function gamesTimecount(){
                  
                var countdownInterval;
                var now = new Date();
                var utcNow = new Date(now.getTime() + now.getTimezoneOffset() * 60000);
                console.log('utcNow==>' + utcNow);
                var scheduleTimeStr = $('#scheduleTime').val();
                var scheduleTime = new Date(scheduleTimeStr);
               
                var scratchCardTimeStr = $('#scratchCard').val();
            
                var quizScheduleTimeStr = $('#quiz').val();
                
                
                
                if (scratchCardTimeStr === '') {
                    console.log('Scratch Card schedule time is empty.');
                    hidesratchCountdown();
                }
                if (quizScheduleTimeStr === '') {
                    console.log('Quiz schedule time is empty.');
                    hidequizCountdown();
                }
                
                var scratchCardTime = new Date(scratchCardTimeStr);
                var quizScheduleTime = new Date(quizScheduleTimeStr);
                
                if (scratchCardTime > utcNow) {
                    initializeCountdown('scratchCardTimer', scratchCardTime,utcNow);
                    $('.scratchcard').prop('disabled', true);
                } else {
                    $('#scratchCardTimer').html('<i class="icon-unlock-alt"></i><span>Unlocks in:</span><span >00:00:00</span>');
                    hidesratchCountdown();
                    var statustype = $('#scratchCard').attr('statustype');
                    console.log(statustype);
                    if (statustype != undefined && statustype =='USED') {
                        $('.scratchcard').prop('disabled', true);
                    }else if(statustype =='VIWED' || statustype =='NEW' ){
                        $('.scratchcard').prop('disabled', false);
                    }else{
                        $('.scratchcard').prop('disabled', true);
                    }
                }
           
            // Initialize quiz countdown if schedule time is in the future
               
            
            if (quizScheduleTime > utcNow) {                
                initializeCountdown('quizTimer', quizScheduleTime,utcNow);
                    $('.dailyquiz').prop('disabled', true);
                   
                } else {
                    $('#quizTimer').html('<i class="icon-unlock-alt"></i><span>Unlocks in:</span><span class="timer">00:00:00</span>');
                    hidequizCountdown();
                    var statustype = $('#quiz').attr('statustype');
                    console.log(statustype);
                    if (statustype != undefined && statustype =='USED') {
                        $('.dailyquiz').prop('disabled', true);
                    }else if(statustype =='VIWED' || statustype =='NEW' ){
                        $('.dailyquiz').prop('disabled', false);
                    }else{
                        $('.dailyquiz').prop('disabled', true);
                    }
                }
    
                // Initialize main countdown timer
                
                if (scheduleTime > utcNow) {
        
                    initializeCountdown('countdown', scheduleTime,utcNow);
                    $('.spinwheel').prop('disabled', true);
                } else {
                    $('#countdown').html('<i class="icon-unlock-alt"></i><span>Unlocks in:</span><span class="timer">00:00:00</span>');
                    hideCountdown();
                    var statustype = $('#scheduleTime').attr('statustype');
                    console.log(statustype);
                    if (statustype != undefined && statustype =='USED') {
                        $('.spinwheel').prop('disabled', true);
                    }else if(statustype =='VIWED' || statustype =='NEW' ){
                        $('.spinwheel').prop('disabled', false);
                    }else{
                        $('.spinwheel').prop('disabled', true);
                    }
                    
                }
        }
                function initializeCountdown(elementId, endTime,utcNow) {
                            
                    var difference = endTime.getTime() - utcNow.getTime();
                    
                    var seconds = Math.floor(difference / 1000);
                    
                    if(difference){
                        var countdownInterval = setInterval(function () {
                            seconds--;
                            var countdownElement = $('#' + elementId);
                            
                            if (seconds <= 0) {
                                $('#' + elementId+' .timer').text("00:00:00");
                                clearInterval(countdownInterval); 
                                if(elementId=='countdown'){
                                hideCountdown();
                                $('.spinwheel').prop('disabled', false);
                                }else if(elementId=='quizTimer'){
                                hidequizCountdown();
                                $('.dailyquiz').prop('disabled', false);
                                }else if(elementId=='scratchCardTimer'){
                                hidesratchCountdown();
                                $('.scratchcard').prop('disabled', false);
                                }
                            } else {
                                countdownElement.html('<i class="icon-unlock-alt"></i><span>Unlocks in:</span><span class="timer" >'+formatTime(seconds)+'</span>');
                                if(elementId=='countdown'){
                                $('#countdown').show();
                                $('.spinwheel').prop('disabled', true);
                                }else if(elementId=='quizTimer'){
                                $('#quizTimer').show();
                                $('.dailyquiz').prop('disabled', true);
                                }else if(elementId=='scratchCardTimer'){
                                $('#scratchCardTimer').show();
                                $('.scratchcard').prop('disabled', true);
                                }
                            }
                        }, 1000);
                    }else{
                        
                        if(elementId=='countdown'){
                            $('#countdown').hide();
                            $('.spinwheel').prop('disabled', false);
                            }else if(elementId=='quizTimer'){
                            $('#quizTimer').hide();
                            $('.dailyquiz').prop('disabled', false);
                            }else if(elementId=='scratchCardTimer'){
                            $('#scratchCardTimer').hide();
                            $('.scratchcard').prop('disabled', false);
                            }
                        
                    }
                }
                function formatTime(seconds) {
                    var hours = Math.floor(seconds / 3600);
                    var minutes = Math.floor((seconds % 3600) / 60);
                    var remainingSeconds = seconds % 60;
                    return pad(hours) + ":" + pad(minutes) + ":" + pad(remainingSeconds);
                }
                function pad(num) {
                    return (num < 10 ? '0' : '') + num;
                }
                function hideCountdown() {
                    $('#countdown').hide();
                    $('.spinwheel').prop('disabled', true);
                }

                function hidequizCountdown() {
                    $('#quizTimer').hide();
                    $('.dailyquiz').prop('disabled', true);
                }

                function hidesratchCountdown() {
                    $('#scratchCardTimer').hide();
                    $('.scratchcard').prop('disabled', true);
                }
        
            });
            
        });
    </script>