<?php
/**
 * @var $block \Comave\LixApiConnector\Block\Offers
 */

$offersDetails = $block->getLixOffersDetailsbyId();
$details = $block->getWalletbalanceDetails();
$lixblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Lixdashboard');
$walletSymbol = $lixblock->getLixxWalletSymbol();
?>
<?php
if (isset ($offersDetails)) {
    if (isset ($offersDetails['success']) && $offersDetails['success'] == true && isset ($offersDetails['data'])) {
        $offerDetail = $offersDetails['data'];
        ?>
        <div class="offer-detail-container row">
            <!-- <div id="claim-offer-errormsg"></div> -->
            <div id="claim-offer-title">
                <?= $block->escapeHtml(__('Offers')) ?>
            </div>
            <div class="offer-details col-lg-12 col-md-12 col-sm-12">
                <div class="details-section">
                    <div class="img-container">
                        <div class="benefit">
                            <h2 class="benefit-text">
                                <?php echo $offerDetail['benefit'] ?? ''; ?>
                            </h2>
                        </div>
                        <img class="offer-image" src="<?php echo $offerDetail['offer_image'] ?? ''; ?>">
                        <div class="offers-note">
                            <span class="note-icon"></span>
                            <span>
                                <?= $block->escapeHtml(__('NB: This offer is only available for customers in')) ?>
                                <?php echo (isset($offerDetail['supported_countries'][0])) ? $block->getCountryName($offerDetail['supported_countries'][0]) : ''; ?>
                            </span>
                        </div>
                    </div>
                    <div class="details-container">
                        <div class="currencies">
                            <label>
                                <?= $block->escapeHtml(__('Choose Wallet')) ?>
                            </label>
                            <?php if (isset($offerDetail['currencies']) && is_array($offerDetail['currencies'])): ?>
                                <?php foreach ($offerDetail['currencies'] as $currency): ?>
                                    <div class="currencies-list row">
                                        <?php
                                            $is_balance = false;
                                            if (isset ($details['success']) && $details['success'] == 1 && isset ($details['data'])) {
                                                foreach ($details['data'] as $detail) {
                                                    if ($detail['currency_symbol'] == $currency['symbol']) {
                                                    ?>
                                                        <div class="radio-button col-lg-1 col-md-1 col-sm-1">
                                                            <input type="radio" name="offer-currency" value="<?php echo $currency['name']; ?>"
                                                                data-symbol="<?php echo $currency['symbol']; ?>"
                                                                data-currencyid="<?php echo $currency['id']; ?>">
                                                        </div>
                                                        <div class="currency col-lg-9 col-md-9 col-sm-9">
                                                            <div class="currencies-name">
                                                                <?php
                                                                echo $currency['name'];
                                                                ?>
                                                            </div>
                                                            <div class="currency-rate">
                                                                <div class="available-bal">
                                                                    <?= $block->escapeHtml(__('Balance:')) ?>
                                                                    <span>
                                                                    <?php
                                                                    if($walletSymbol == $currency['symbol']){
                                                                        echo number_format((float)$detail['max_balance'], 3, '.', '')." ".$currency['symbol'];
                                                                    }
                                                                    else{
                                                                        echo $detail['balance']." ".$currency['symbol'];
                                                                    }
                                                                    ?>
                                                                    </span>
                                                                </div>
                                                                <div class="currencies-rate-container">
                                                                <?= $block->escapeHtml(__('Offer amount:')) ?>
                                                                <span>
                                                                    <div class="currencies-rate">
                                                                        <div class="currency-rates" data-rate="<?php echo $currency['pivot']['fee']; ?>">
                                                                            <span>
                                                                                <?php echo $currency['pivot']['fee']; ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <?php
                                                                    echo " ".$currency['symbol'];
                                                                    ?>
                                                                </span>
                                                             </div>
                                                            </div>
                                                        </div>
                                                        <?php
                                                        $is_balance = true;
                                                        break;
                                                    }
                                                }
                                                if (!$is_balance) {
                                                    ?>
                                                    <div class="radio-button col-lg-1 col-md-1 col-sm-1">
                                                        <input type="radio" name="offer-currency" value="<?php echo $currency['name']; ?>"
                                                            data-symbol="<?php echo $currency['symbol']; ?>"
                                                            data-currencyid="<?php echo $currency['id']; ?>">
                                                    </div>
                                                    <div class="currency col-lg-9 col-md-9 col-sm-9">
                                                        <div class="currencies-name">
                                                            <?php
                                                            echo $currency['name'];
                                                            ?>
                                                        </div>
                                                        <div class="currency-rate">
                                                            <div class="available-bal">
                                                                <?= $block->escapeHtml(__('Balance:')) ?>
                                                                <span>
                                                                <?php
                                                                echo "0"." ".$currency['symbol'];
                                                                ?>
                                                                </span>
                                                             </div>
                                                             <div class="currencies-rate-container">
                                                                <?= $block->escapeHtml(__('Offer amount:')) ?>
                                                                <span>
                                                                     <div class="currencies-rate">
                                                                        <div class="currency-rates" data-rate="<?php echo $currency['pivot']['fee']; ?>">
                                                                            <span>
                                                                                <?php echo $currency['pivot']['fee']; ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <?php
                                                                    echo " ".$currency['symbol'];
                                                                    ?>
                                                                </span>
                                                             </div>
                                                        </div>
                                                    </div>
                                                <?php
                                                }
                                            }
                                        if ($currency['pivot']['is_discount'] == 1) {
                                        ?>
                                            <div class="discount col-lg-2 col-md-2 col-sm-2">
                                                <span>
                                                    <?php
                                                    echo $currency['pivot']['discount'] . "% off";
                                                    ?>
                                                </span>
                                            </div>
                                            <?php
                                        }
                                        else{
                                            ?>
                                             <div class="discount col-lg-2 col-md-2 col-sm-2" style="display: none;">
                                                <span>
                                                    <?php
                                                    echo "0 % off";
                                                    ?>
                                                </span>
                                            </div>
                                        <?php
                                        }
                                        ?>
                                        <div class="gasfee" style="display: none;">
                                            <span>
                                                <?php
                                                $mgt_fee = ($currency['pivot']['fee'] * $offerDetail['management_fee']) / 100;
                                                echo $mgt_fee;
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach;?>
                            <?php endif; ?>
                        </div>
                        <div id="currency-errormsg"></div>
                        <div id="package-errormsg"></div>
                        <!-- Popup container -->
                        <div class="popup-main">
                            <div class="claim-offer-popup-container" id="claim-offer-popup">
                                <span class="claim-offer-popup-close">&times;</span>
                                <div class="claim-offer-popup-head">
                                    <h2>
                                        <?php echo $offerDetail['benefit'] ?? ''; ?>
                                    </h2>
                                </div>
                                <div class="claim-offer-popup-body row">
                                    <div class="order-preview">
                                        <?= $block->escapeHtml(__('Order Preview')) ?>
                                    </div>
                                    <div class="summary-wallet row">
                                        <div class="summary-wallet-label col-lg-6 col-md-6 col-sm-6">
                                            <div class="wallet">
                                                <?= $block->escapeHtml(__('Wallet balance:')) ?>
                                            </div>
                                        </div>
                                        <div class="summary-wallet-value col-lg-6 col-md-6 col-sm-6">
                                            <div class="wallet-value">
                                                <div id="wallet-popup"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="summary-coupon row">
                                        <div class="summary-coupon-label col-lg-6 col-md-6 col-sm-6">
                                            <div class="coupon">
                                                <?= $block->escapeHtml(__('Coupon:')) ?>
                                            </div>
                                        </div>
                                        <div class="summary-coupon-value col-lg-6 col-md-6 col-sm-6">
                                            <div class="coupon-value">
                                                <div id="coupon-popup"></div>
                                                <div id="coupon-currency-popup"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="summary-discount row">
                                        <div class="summary-discount-label col-lg-6 col-md-6 col-sm-6">
                                            <div class="discount">
                                                <?= $block->escapeHtml(__('Discount:')) ?>
                                            </div>
                                        </div>
                                        <div class="summary-discount-value col-lg-6 col-md-6 col-sm-6">
                                            <div class="discount-value" id="discount-popup"></div>
                                        </div>
                                    </div>
                                    <div class="summary-gas-fee row">
                                        <div class="summary-gas-fee-label col-lg-6 col-md-6 col-sm-6">
                                            <div class="gas-fee">
                                                <?= $block->escapeHtml(__('Management fee:')) ?>
                                            </div>
                                        </div>
                                        <div class="summary-gas-fee-value col-lg-6 col-md-6 col-sm-6">
                                            <div class="gas-fee-value" id="gasfee-popup"></div>
                                        </div>
                                    </div>
                                    <div class="summary-total row">
                                        <div class="total-label col-lg-6 col-md-6 col-sm-6">
                                            <div class="total">
                                                <?= $block->escapeHtml(__('Total:')) ?>
                                            </div>
                                        </div>
                                        <div class="total-value col-lg-6 col-md-6 col-sm-6">
                                            <div class="total-value" id="total-popup"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="claim-offer-popup-footer" data-offer-id="<?php echo $offerDetail['id'] ?? ''; ?>" >
                                    <button class="popup-claim-offer">
                                        <?= $block->escapeHtml(__('Claim Offer')) ?>
                                    </button>
                                </div>
                                <!-- Loader element  -->
                                <div id="coupon-loader" style="display: none;">
                                    <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>"
                                        alt="Loading...">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="about-section">
                    <?php if (isset($offerDetail['instructions'])): ?>
                        <div class="about-deal">
                            <label>About this deal</label>
                            <?php
                            $instruction = $offerDetail['instructions'];
                            $ins_newline = str_replace("\\n", " </br>", $instruction);
                            $ins_space = str_replace("\\", " ", $ins_newline);
                            $instructions = preg_replace('/(https?:\/\/\S+)/', '<a href="$1">$1</a>', $ins_space);
                            $instruction_decode = html_entity_decode($instructions);
                            ?>
                            <p>
                                <?php echo $instruction_decode; ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="bottom-summary">
            <div class="summary-container" style="display: none;">
                <label>
                    <?= $block->escapeHtml(__('Summary')) ?>
                </label>
                <div class="summary-coupon row">
                    <div class="summary-coupon-label col-lg-6 col-md-6 col-sm-6">
                        <div class="coupon"><?= $block->escapeHtml(__('Coupon')) ?></div>
                    </div>
                    <div class="summary-coupon-value col-lg-6 col-md-6 col-sm-6">
                        <div class="coupon-value">
                            <div id="coupon"></div>
                            <div id="coupon-currency"></div>
                        </div>
                    </div>
                </div>
                <div class="summary-discount row">
                    <div class="summary-discount-label col-lg-6 col-md-6 col-sm-6">
                        <div class="discount"><?= $block->escapeHtml(__('Discount')) ?></div>
                    </div>
                    <div class="summary-discount-value col-lg-6 col-md-6 col-sm-6">
                        <div class="discount-value" id="discount"></div>
                    </div>
                </div>
                <div class="summary-gas-fee row">
                    <div class="summary-gas-fee-label col-lg-6 col-md-6 col-sm-6">
                        <div class="gas-fee"><?= $block->escapeHtml(__('Management fee')) ?></div>
                    </div>
                    <div class="summary-gas-fee-value col-lg-6 col-md-6 col-sm-6">
                        <div class="gas-fee-value" id="gasfee"></div>
                    </div>
                </div>
                <div class="summary-total row">
                    <div class="total-label col-lg-6 col-md-6 col-sm-6">
                        <div class="total"><?= $block->escapeHtml(__('Total')) ?></div>
                    </div>
                    <div class="total-value col-lg-6 col-md-6 col-sm-6">
                        <div class="total-value" id="total"></div>
                    </div>
                </div>

            </div>
            <div id="earn-reward-msg"></div>
            <a href="<?= $this->getUrl('lixreward/EarnReward/earn/') ?>" class="earn-reward-button">
                <button class="earn-reward"><?= $block->escapeHtml(__('Earn Reward')) ?></button>
            </a>
            <div class="reward-button">
                <button class="claim-offer" style="display:none;"><?= $block->escapeHtml(__('Claim Offer')) ?></button>
            </div>
        </div>
    <?php
    } else {
            echo "Market offer details not found";
    }
} else {
        echo "No data found==";
}
?>
<!-- Coupon container -->
<div class="popup-success">
    <div class="coupon-popup-container" id="coupon-data-popup">
        <span class="coupon-popup-close">&times;</span>
        <div class="coupon-body row">
            <div class="successful-logo">
                <img class="organisation-logo"
                    src="<?php echo $block->getMediaUrl() . 'wysiwyg/success_coupon.png'; ?>">
            </div>
            <div class="coupon-desc"></div>
            <div class="coupon-msg"></div>
            <div class="coupon-url">
                <a href="" target="_blank" id="coupon-url">
                </a>
            </div>
            <div class="show-qrcode">
                <button class="coupon-qrcode"><?= $block->escapeHtml(__('Show QR Code')) ?></button>
                <button class="hide-coupon-qrcode" style="display: none;"><?= $block->escapeHtml(__('Hide QR Code')) ?></button>
                <div>
                    <img class="coupon-qrcode-img" src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=" alt="QR Code" style="display: none;">
                </div>
            </div>
            <div class="show-barcode">
                <button class="coupon-barcode"><?= $block->escapeHtml(__('Show Barcode')) ?></button>
                <button class="hide-coupon-barcode" style="display: none;"><?= $block->escapeHtml(__('Hide Barcode')) ?></button>
                <div>
                    <img class="coupon-barcode-img" src="data:image/png;base64," alt="Barcode" style="display: none;">
                </div>
            </div>
<!--             <div class="share-now">
                <div class="share-earn">Share & Earn</div>
                <div class="share-earn-text">Share this discount offer with your friends and earn <div
                        class="earn-points">5 LIX</div>
                </div>
                <button class="share">Share Now</button>
            </div> -->
        </div>
    </div>
</div>
<div class="error-message">
    <div class="popup-message" id="err-message" style="display:none;">
        <div class="popup-content-message">
            <span class="err-close-btn">&times;</span>
            <h4>
                <?= $block->escapeHtml(__('Alert Message')) ?>
            </h4>
            <div class="messages">
                <div id="claim-offer-errormsg"></div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        // var offerPackageRadios = document.querySelectorAll('input[name="offer-package"]');
        // offerPackageRadios.forEach(function (radio) {
        //     radio.addEventListener("change", function () {
        //         if (this.checked) {
        //             var selectedValue = this.value;
        //             var selectedcurrency = this.getAttribute("data-currency")
        //             var selectedcurrencysymbol = this.getAttribute("data-currency-symbol");
        //             document.getElementById("coupon-currency").innerHTML = " | " + selectedValue + " " + selectedcurrency;
        //             document.getElementById("coupon-currency-popup").innerHTML = " | " + selectedValue + " " + selectedcurrency;
        //         }
        //     });
        // });

        // var initiallySelectedRadio = document.querySelector('input[name="offer-package"]:checked');
        // if (initiallySelectedRadio) {
        //     var selectedValue = initiallySelectedRadio.value;
        //     var selectedcurrency = initiallySelectedRadio.getAttribute("data-currency");
        //     var selectedcurrencysymbol = initiallySelectedRadio.getAttribute("data-currency-symbol");
        //     document.getElementById("coupon-currency").innerHTML = " | " + selectedValue + " " + selectedcurrency;
        //     document.getElementById("coupon-currency-popup").innerHTML = " | " + selectedValue + " " + selectedcurrency;
        // }

        var offerCurrencyRadios = document.querySelectorAll('input[name="offer-currency"]');
        offerCurrencyRadios.forEach(function (radio) {
            radio.addEventListener("change", function () {
                if (this.checked) {
                    document.getElementById("currency-errormsg").innerHTML = " ";
                    document.querySelector(".summary-container").style.display = "block";
                    var selectedCurrencyValue = this.value;
                    var selectedCurrencySymbol = this.getAttribute("data-symbol");
                    var currencyrateText = this.closest('.currencies-list').querySelector('.currencies-rate').innerText;
                    var currencyrate = parseFloat(currencyrateText);
                    var discountText = this.closest('.currencies-list').querySelector('.discount span').innerText;
                    var discount = parseFloat(discountText);
                    var gasfeeText = this.closest('.currencies-list').querySelector('.gasfee span').innerText;
                    var gasfee = parseFloat(gasfeeText);
                    var calDisc = (currencyrate * discount) / 100;
                    var calDiscount = Math.round(calDisc * 1000000) / 1000000;
                    document.getElementById("coupon").innerHTML = currencyrate + " " + selectedCurrencySymbol;
                    document.getElementById("coupon-popup").innerHTML = currencyrate + " " + selectedCurrencySymbol;
                    document.getElementById("discount").innerHTML = "- " + calDiscount + " " + selectedCurrencySymbol;
                    document.getElementById("discount-popup").innerHTML = "- " + calDiscount + " " + selectedCurrencySymbol;
                    var totalpoints = (currencyrate - calDiscount) + gasfee;
                    var total = Math.round(totalpoints * 1000000) / 1000000;
                    document.getElementById("total").innerHTML = total + " " + selectedCurrencySymbol;
                    document.getElementById("total-popup").innerHTML = total + " " + selectedCurrencySymbol;
                    document.getElementById("gasfee").innerHTML = gasfee + " " + selectedCurrencySymbol;
                    document.getElementById("gasfee-popup").innerHTML = gasfee + " " + selectedCurrencySymbol;
                    var availableBalances = getAvailableBalance(selectedCurrencySymbol);
                    if (availableBalances !== null) {
                        document.getElementById("wallet-popup").innerHTML = availableBalances + " " + selectedCurrencySymbol;
                        if (availableBalances >= total) {
                            document.querySelector(".claim-offer").style.display = "block";
                            document.querySelector(".earn-reward").style.display = "none";
                            document.getElementById("earn-reward-msg").innerHTML = " ";
                        }
                        else {
                            document.querySelector(".claim-offer").style.display = "none";
                            document.querySelector(".earn-reward").style.display = "block";
                            document.getElementById("earn-reward-msg").innerHTML = "You do not have sufficient rewards to claim this offer. Please, Earn Rewards by clicking here.";
                        }
                    }
                    else {
                        document.querySelector(".claim-offer").style.display = "none";
                        document.querySelector(".earn-reward").style.display = "block";
                        document.getElementById("earn-reward-msg").innerHTML = "You do not have sufficient rewards to claim this offer. Please, Earn Rewards by clicking here.";
                    }
                }
            });
        });

    });
    function getAvailableBalance(currencySymbol) {
        var availableBalances = <?php echo (isset($details['data']) && $details['data'] !== false)
            ? json_encode($details['data']) : '[]'; ?>;
        var walletSymbol = "<?php echo $lixblock->getLixxWalletSymbol(); ?>";
        for (var i = 0; i < availableBalances.length; i++) {
            if (availableBalances[i].currency_symbol === currencySymbol) {
                if(walletSymbol == currencySymbol){
                    return availableBalances[i].max_balance;
                }
                else{
                    return availableBalances[i].balance;
                }
            }
        }
        return null;
    }
    require(['jquery'], function ($) {
        $(document).ready(function () {
            $(document).on('click', '.coupon-qrcode', function () {
                $('.coupon-qrcode-img').css('display', 'inline-block');
                $('.hide-coupon-qrcode').show();
                $(this).hide();
            });

            $(document).on('click', '.hide-coupon-qrcode', function() {
                $('.coupon-qrcode-img').hide();
                $('.coupon-qrcode').show();
                $(this).hide();
            });

            $(document).on('click', '.coupon-barcode', function () {
                $('.coupon-barcode-img').css('display', 'inline-block');
                $('.hide-coupon-barcode').show();
                $(this).hide();
            });

            $(document).on('click', '.hide-coupon-barcode', function() {
                $('.coupon-barcode-img').hide();
                $('.coupon-barcode').show();
                $(this).hide();
            });

            $(document).on('change', 'input[name="offer-package"]', function (event) {
                event.preventDefault();
                $('#earn-reward-msg').html('');
                var pricingValue = $(this).val();
                $('.currency-rates').each(function (index) {
                    var rate = $(this).data('rate');
                    var rateCon = pricingValue / rate;
                    var rateConversion = Math.round(rateCon * 1000000) / 1000000;
                    $(this).find("span").html(rateConversion);
                });
                // var initiallySelectedoffercurrency = $('input[name="offer-currency"]:checked');
                // if (initiallySelectedoffercurrency.length > 0) {
                //     var selectedCurrencyValue = initiallySelectedoffercurrency.val();
                //     var selectedCurrencySymbol = initiallySelectedoffercurrency.data("symbol");
                //     var currencyrateText = initiallySelectedoffercurrency.closest('.currencies-list').find('.currencies-rate').text();
                //     var currencyrate = parseFloat(currencyrateText);
                //     var discountText = initiallySelectedoffercurrency.closest('.currencies-list').find('.discount span').text();
                //     var discount = parseFloat(discountText);
                //     var gasfeeText = initiallySelectedoffercurrency.closest('.currencies-list').find('.gasfee span').text();
                //     var gasfee = parseFloat(gasfeeText);
                //     var calDisc = (currencyrate * discount) / 100;
                //     var calDiscount = Math.round(calDisc * 1000000) / 1000000;
                //     $("#coupon").html(currencyrate + " " + selectedCurrencySymbol);
                //     $("#coupon-popup").html(currencyrate + " " + selectedCurrencySymbol);
                //     $("#discount").html("- " + calDiscount + " " + selectedCurrencySymbol);
                //     $("#discount-popup").html("- " + calDiscount + " " + selectedCurrencySymbol);
                //     var totalpoints = (currencyrate - calDiscount) + gasfee;
                //     var total = Math.round(totalpoints * 1000000) / 1000000;
                //     $("#total").html(total + " " + selectedCurrencySymbol);
                //     $("#total-popup").html(total + " " + selectedCurrencySymbol);
                //     $("#gasfee").html(gasfee + " " + selectedCurrencySymbol);
                //     $("#gasfee-popup").html(gasfee + " " + selectedCurrencySymbol);
                //     var availableBalances = getAvailableBalance(selectedCurrencySymbol);
                //     if (availableBalances !== null) {
                //         $("#wallet-popup").html(availableBalances + " " + selectedCurrencySymbol);
                //         if (availableBalances >= total) {
                //             document.querySelector(".claim-offer").style.display = "block";
                //             document.querySelector(".earn-reward").style.display = "none";
                //             $("#earn-reward-msg").html(" ");
                //         }
                //         else {
                //             document.querySelector(".claim-offer").style.display = "none";
                //             document.querySelector(".earn-reward").style.display = "block";
                //             $("#earn-reward-msg").html("You do not have sufficient points to complete this offer. Please, Earn Rewards by clicking here.");
                //         }
                //     }
                //     else {
                //         document.querySelector(".claim-offer").style.display = "none";
                //         document.querySelector(".earn-reward").style.display = "block";
                //         $("#earn-reward-msg").html("You do not have sufficient points to complete this offer. Please, Earn Rewards by clicking here.");
                //     }
                // }
            });

            $('.earn-reward-button button').click(function (e) {
                $('#currency-errormsg').html('');
                $('#package-errormsg').html('');
                e.preventDefault();
                var selectedCurrency = $('input[name="offer-currency"]:checked').val();
                //var selectedPackage = $('input[name="offer-package"]:checked').val();

                if (!selectedCurrency) {
                    document.getElementById("currency-errormsg").innerHTML = "Please select wallet";
                    return;
                }
                // if (!selectedPackage) {
                //     document.getElementById("package-errormsg").innerHTML = "Please selct offer package";
                //     return;
                // }
            });


            $('.claim-offer').click(function (e) {
                e.preventDefault();
                var bodyClasses = document.body.className;
                var newClass = 'order-preview';
                document.body.classList.add(newClass);
                $('#claim-offer-popup').show();

            });
            $('.claim-offer-popup-close').click(function (e) {
                e.preventDefault();
                var newClass = 'order-preview';
                if (document.body.classList.contains(newClass)) {
                    document.body.classList.remove(newClass);
                }
                $('#claim-offer-popup').hide();

            });

            $('.claim-offer-popup-footer button').click(function (e) {
                e.preventDefault();

                var offerId = $(this).closest('.claim-offer-popup-footer').data('offer-id');
                // var brandName = $(this).closest('.claim-offer-popup-footer').data('brand-name');
                // var offerCurrency = $(this).closest('.claim-offer-popup-footer').data('offer-currency');
                //var selectedPackage = $('input[name="offer-package"]:checked').val();
                var selectedCurrency = $('input[name="offer-currency"]:checked');
                if (selectedCurrency.length > 0) {
                    var selectedCurrencyId = selectedCurrency.data("currencyid");
                    var currencyrateText = selectedCurrency.closest('.currencies-list').find('.currencies-rate').text();
                    var currencyrate = parseFloat(currencyrateText);
                    var discountText = selectedCurrency.closest('.currencies-list').find('.discount span').text();
                    var discount = parseFloat(discountText);
                    var gasfeeText = selectedCurrency.closest('.currencies-list').find('.gasfee span').text();
                    var gasfee = parseFloat(gasfeeText);
                    var calDisc = (currencyrate * discount) / 100;
                    var calDiscount = Math.round(calDisc * 1000000) / 1000000;
                    var totalpoints = (currencyrate - calDiscount) + gasfee;
                    var total = Math.round(totalpoints * 1000000) / 1000000;
                }
                var formData = {
                    item_id: offerId,
                    custom_currency_id: selectedCurrencyId,
                };
                $.ajax({
                    url: "<?php echo $block->getLixCouponDetails(); ?>",
                    method: 'POST',
                    data: formData,
                    beforeSend: function () {
                        $('#coupon-loader').show();
                    },
                    success: function (response) {
                        $('#claim-offer-popup').hide();
                        if (response.status == "success") {
                            $(".coupon-desc").html(response.responseData.data.coupon.description);
                            $(".coupon-msg").html(response.responseData.message);
                            $("#coupon-url").attr("href", response.responseData.data.coupon.coupon);
                            $("#coupon-url").html(response.responseData.data.coupon.coupon);
                            var qrCodeDataUrl = "https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=" + response.responseData.data.coupon.coupon_qrcode;
                            $('.coupon-qrcode-img').attr('src', qrCodeDataUrl);
                            var barCodeDataUrl = "data:image/png;base64," + response.responseData.data.coupon.coupon_barcode;
                            $('.coupon-barcode-img').attr('src', barCodeDataUrl);
                            var bodyClasses = document.body.className;
                            var newClass = 'coupon-preview';
                            document.body.classList.add(newClass);
                            $('#coupon-data-popup').show();
                        }
                        else {
                            var newClass = 'order-preview';
                            if (document.body.classList.contains(newClass)) {
                                document.body.classList.remove(newClass);
                            }
                            var bodyerrClasses = document.body.className;
                            var newerrClass = 'err-msg';
                            document.body.classList.add(newerrClass);
                            $('#err-message').show();
                            document.getElementById("claim-offer-errormsg").innerHTML = response.responseData.message;
                            return;
                        }

                    },
                    error: function (xhr, status, error) {
                    },
                    complete: function () {
                        $('#coupon-loader').hide();
                    }
                });
            });
            $('.coupon-popup-close').click(function (e) {
                e.preventDefault();
                var newClass = 'coupon-preview';
                if (document.body.classList.contains(newClass)) {
                    document.body.classList.remove(newClass);
                }
                var neworderClass = 'order-preview';
                if (document.body.classList.contains(neworderClass)) {
                    document.body.classList.remove(neworderClass);
                }
                $('#coupon-data-popup').hide();
                location.reload(true);

            });

            $('.err-close-btn').click(function (e) {
                e.preventDefault();
                var newerrClass = 'err-msg';
                if (document.body.classList.contains(newerrClass)) {
                    document.body.classList.remove(newerrClass);
                }
                $('#err-message').hide();

            });
        });
    });

</script>
