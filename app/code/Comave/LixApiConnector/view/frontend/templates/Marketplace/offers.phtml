<?php
$offers = $block->getMarketOffersDetails();
$lixoffers = $block->getLixOffersDetails();
$pageValue = $this->getData('page'); 
//echo $pageValue; 
$category = $block->getCategory();
if(isset($category) && $category['success'] == 1){ 
    $categoryData = $category['data']['data'];   
}
$country = $block->getCountry();
if(isset($country) && $country['success'] == 1){ 
    $countryData = $country['data']['data']; 
}
$paramsData = $block->getParmasDetail();
$keyword = $paramsData['search'] ?? '';
$category = $paramsData['categories'] ?? '';
$country = $paramsData['countries'] ?? '';
$orderby = $paramsData['orderby'] ?? '';

?>
<div class="offers-list">
    <div class="sort-section">
        <div class="filter-offer row">
            <form id="filterForm" method="get">
                <div class="category col-lg-4 col-md-4 col-sm-6">
                    <select name="categories" id="categorySelect">
                        <option value="">
                            <?= $block->escapeHtml(__('Category')) ?>
                        </option>
                        <?php 
                        if (isset($categoryData)) {
                            foreach($categoryData as $categories){ 
                               ?>
                                <option value="<?php echo $categories['name']; ?>" <?php
                                    if($category==$categories['name']){echo "selected" ;} ?>>
                                    <?php echo ucwords($categories['name']); ?>
                                </option>
                                <?php
                            }
                        } else {
                            ?>
                        <option value="">
                            <?php echo $block->escapeHtml(__('No categories available')); ?>
                        </option>
                        <?php
                        }
                    ?>
                    </select>
                </div>
                <div class="country col-lg-4 col-md-4 col-sm-6">
                    <select name="countries" id="countrySelect">
                        <option value="">
                            <?= $block->escapeHtml(__('Country')) ?>
                        </option>
                        <?php 
                        if (isset($countryData)) {
                            foreach($countryData as $countries){ 
                               ?>
                                <option value="<?php echo $countries['id']; ?>" <?php
                                    if($country==$countries['id']){echo "selected" ;} ?>>
                                    <?php echo ucwords($countries['name']); ?>
                                </option>
                                <?php
                            }
                        } else {
                            ?>
                        <option value="">
                            <?php echo $block->escapeHtml(__('No countries available')); ?>
                        </option>
                        <?php
                        }
                    ?>
                    </select>
                </div>
                <div class="order-by col-lg-4 col-md-4 col-sm-6">
                    <select name="orderby" id="orderbySelect">
                        <option value="">
                            <?= $block->escapeHtml(__('Order By')) ?>
                        </option>
                        <option value="asc" <?php if($orderby=="asc" ){echo "selected" ;} ?>>
                            <?= $block->escapeHtml(__('Name A to Z')) ?>
                        </option>
                        <option value="desc" <?php if($orderby=="desc" ){echo "selected" ;} ?>>
                            <?= $block->escapeHtml(__('Name Z to A')) ?>
                        </option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button type="submit" title="Search" class="action filter-search" aria-label="Search">
                        <span class="offer-search"></span>
                    </button>
                </div>
                <button type="button" class="action clear-filters" aria-label="Clear Filters">
                    <?= $block->escapeHtml(__('Clear Filters')) ?>
                </button>
            </form>
        </div>
        <div class="search-offer row">
            <div class="keyword col-lg-12 col-md-12 col-sm-12">
                <form class="keyword" method="get">
                    <input type="text" name="search" placeholder="Search" id="searchInput"
                        value="<?php if($keyword){echo $keyword;}?>">
                    <span class="cancel-button" id="cancelButton">&times;</span>
                    <div class="actions">
                        <button type="submit" title="Search" class="action search" aria-label="Search">
                            <span class="offer-search">search</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="offers row">
        <?php 
            if (isset($lixoffers['success']) && $lixoffers['success'] == 1 && isset($lixoffers['data']) && isset($lixoffers['data']['data'])) {
                $lixoffersData = $lixoffers['data']['data'];
                foreach($lixoffersData as $itemData){ 
                   ?>
                    <div class="offers col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="offers-grid">
                            <div class="offer-img-container">
                                <img src="<?php echo $itemData['offer_image']; ?>">
                            </div>
                            <div class="offer-details-container">
                                <h4 class="brand_name">
                                    <?= $escaper->escapeHtml($itemData['benefit']); ?>
                                </h4>
                                <!-- <h4 class="benefit">
                                    <?= $escaper->escapeHtml($itemData['benefit']); ?>
                                </h4> -->
                                <div class="currency-wallet">
                                    <div class="lix-rewards">
                                        <div class="wallets-list">
                                            <img src="<?php echo $itemData['currencies'][0]['avatar']; ?>">
                                            <span>
                                                <?= $escaper->escapeHtml($itemData['currencies'][0]['symbol']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ticketshape">
                            </div>
                            <div class="ticketSubDetail">
                                <?php if ($block->isCustomerLoggedIn()): ?>
                                    <a href="<?php echo $block->getLixofferDetailPageUrl($itemData['id']); ?>" class="redeem-link">
                                        <button class="red">
                                            <?= $block->escapeHtml(__('redeem')) ?>
                                        </button>
                                    </a>
                                <?php else: ?>
                                    <button class="red login-to-redeem">
                                        <?= $block->escapeHtml(__('redeem')) ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                 echo $block->escapeHtml(__('No offers available'));
            }
        ?>

        <?php 
            if (isset($offers['success']) && $offers['success'] == 1 && isset($offers['data']) && isset($offers['data']['marketplace'])) {
                $offersData = $offers['data']['marketplace'];
                foreach($offersData['data'] as $item){ 
                   ?>
                    <div class="offers col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="offers-grid">
                            <div class="offer-img-container">
                                <img src="<?php echo $item['offer_image']; ?>">
                            </div>
                            <div class="offer-details-container">
                                <h4 class="brand_name">
                                    <?= $escaper->escapeHtml($item['brand_name']); ?>
                                </h4>
                                <h4 class="benefit">
                                    <?= $escaper->escapeHtml($item['benefit']); ?>
                                </h4>
                                <div class="currency-wallet">
                                    <div class="offer-currency">
                                        <div class="offer-currency-img"></div>
                                        <span>
                                            <?= $escaper->escapeHtml($item['offer_currency']); ?>
                                        </span>
                                    </div>
                                    <div class="lix-rewards">
                                        <div class="wallets-list">
                                            <img src="<?php echo $item['currencies'][0]['avatar']; ?>">
                                            <span>
                                                <?= $escaper->escapeHtml($item['currencies'][0]['symbol']); ?>
                                            </span>
                                        </div>
                                        <div class="currencies-popup">
                                            <?= $block->escapeHtml(__('More')) ?>
                                        </div>
                                        <div class="popup">
                                            <div class="popup-content">
                                                <span class="close-btn">&times;</span>
                                                <h4>
                                                    <?= $block->escapeHtml(__('LIX Wallets')) ?>
                                                </h4>
                                                <div class="all-currencies">
                                                    <?php foreach ($item['cashpoints'] as $cashpoint) { ?>
                                                    <div class="currency">
                                                        <img src="<?php echo $cashpoint['avatar']; ?>">
                                                        <div class="currency-name">
                                                            <?= $escaper->escapeHtml($cashpoint['symbol']); ?>
                                                        </div>
                                                    </div>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ticketshape">
                            </div>
                            <div class="ticketSubDetail">
                                <?php if ($block->isCustomerLoggedIn()): ?>
                                    <a href="<?php echo $block->getofferDetailPageUrl($item['id']); ?>" class="redeem-link">
                                        <button class="red">
                                            <?= $block->escapeHtml(__('redeem')) ?>
                                        </button>
                                    </a>
                                <?php else: ?>
                                    <button class="red login-to-redeem">
                                        <?= $block->escapeHtml(__('redeem')) ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                 echo $block->escapeHtml(__('No offers available'));
            }
        ?>
    </div>
    <nav aria-label="Page navigation example row">
        <ul class="offers pagination">
            <?php
            if (isset($offers['success']) && $offers['success'] == 1 && isset($offers['data']) && isset($offers['data']['marketplace'])) {
                foreach ($offersData['links'] as $link){
                    if ($link['active']) { ?>
                        <li class="page-item active" aria-current="page">
                            <span class="page-link">
                                <?= $link['label'] ?>
                            </span>
                        </li>
                        <?php 
                    } else if ($link['url']){ ?>
                        <li class="page-item"><a class="page-link" href="/lixreward/marketplace/offers<?= $link['url'] ?>">
                                <?= $link['label'] ?>
                            </a></li>
                    <?php 
                    } else { ?>
                    <li class="page-item disabled"><span class="page-link">
                            <?= $link['label'] ?>
                        </span></li>
                    <?php 
                    }
                }
            }
            ?>
        </ul>
    </nav>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        var moreButtons = document.querySelectorAll('.currencies-popup');
        moreButtons.forEach(function (moreButton) {
            moreButton.addEventListener('click', function (event) {
                event.stopPropagation(); 
                var popup = this.nextElementSibling; 
                document.querySelectorAll('.popup.show').forEach(function(openPopup) {
                    if (openPopup !== popup) {
                        openPopup.classList.remove('show');
                    }
                });
                popup.classList.toggle('show');
            });
        });

        var closeButtons = document.querySelectorAll('.close-btn');
        closeButtons.forEach(function (closeButton) {
            closeButton.addEventListener('click', function () {
                var popup = this.closest('.popup'); 
                popup.classList.remove('show');
            });
        });

        document.addEventListener('click', function (e) {
            var openPopups = document.querySelectorAll('.popup.show');
            openPopups.forEach(function (popup) {
                if (!popup.contains(e.target)) { 
                    popup.classList.remove('show');
                }
            });
        });
        var categorySelect = document.getElementById('categorySelect');
        var countrySelect = document.getElementById('countrySelect');
        var orderbySelect = document.getElementById('orderbySelect');
        categorySelect.addEventListener('change', function () {
            document.getElementById('filterForm').submit();
        });

        countrySelect.addEventListener('change', function () {
            document.getElementById('filterForm').submit();
        });

        orderbySelect.addEventListener('change', function () {
            document.getElementById('filterForm').submit();
        });
        var clearFiltersButton = document.querySelector('.clear-filters');
        clearFiltersButton.addEventListener('click', function () {
            var url = window.location.origin + window.location.pathname;
            window.location.href = url;
        });

        const searchInput = document.getElementById('searchInput');
        const cancelButton = document.getElementById('cancelButton');
        if (searchInput.value.trim() !== '') {
            cancelButton.style.display = 'inline-block';
        }
        searchInput.addEventListener('input', function (event) {
            if (event.target.value.trim() !== '') {
                cancelButton.style.display = 'inline-block';
            } else {
                cancelButton.style.display = 'none';
            }
        });
        cancelButton.addEventListener('click', function () {
            var url = window.location.origin + window.location.pathname;
            window.location.href = url;
        });

    });
</script>
<script type="text/javascript">
    var loginButtons = document.querySelectorAll('.login-to-redeem');
    loginButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            var loginUrl = '<?php echo $block->getLoginUrl(); ?>';
            alert('<?= $block->escapeJs(__('Please login to redeem this offer.')) ?>');
            window.location.href = loginUrl;
        });
    });
</script>