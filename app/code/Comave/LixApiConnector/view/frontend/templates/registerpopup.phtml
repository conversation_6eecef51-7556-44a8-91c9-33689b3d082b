<!-- Registration Success Popup -->
<div id="registration-success-popup" style="display: none;">
   <h2>Register Successfully!</h2>
</div>

<!-- Notification Allow/Deny Popup -->
<div id="notification-popup" style="display: none;">
   <h2>Allow Notifications?</h2>
   <p>Would you like to receive notifications?</p>
</div>

<script>
   require(
       [
           'jquery',
           'Magento_Ui/js/modal/modal',
           'mage/cookies'
       ],
       function (
           $,
           modal
       ) {
           // Check if the registration success cookie is set
           if ($.cookie('registration_success') == 1) {
               // Show registration success popup
               $('#registration-success-popup').show();
               var registrationSuccessModal = modal({
                   type: 'popup',
                   modalClass: 'my-popup',
                   responsive: true,
                   innerScroll: true,
                   buttons: []
               }, $('#registration-success-popup'));
               $('#registration-success-popup').modal('openModal');

               // Delete the registration_success cookie after displaying the popup
               $.removeCookie('registration_success', { path: '/' });

               // Wait for registration success popup to close
               $('#registration-success-popup').on('modalclosed', function() {
                   // Show notification allow/deny popup
                   $('#notification-popup').show();

                   // Handle user interaction based on confirmation
                   var allowNotifications = confirm('Would you like to allow notifications?');
                   if (allowNotifications) {
                       // Request permission to show notifications
                       Notification.requestPermission().then(function(permission) {
                           if (permission === 'granted') {
                               alert('Notifications allowed');
                               // Save the preference via AJAX
                               saveNotificationPreference(1);
                           } else {
                               alert('Notifications denied');
                               saveNotificationPreference(0);
                           }
                       });
                   } else {
                       alert('Notifications denied');
                       saveNotificationPreference(0);
                   }

                   // Hide the notification popup after interaction
                   $('#notification-popup').hide();
               });
           }

           function saveNotificationPreference(value) {
               $.ajax({
                   url: '/lixreward/index/saveNotificationPreference',
                   type: 'POST',
                   data: { notification_allowed: value },
                   success: function(response) {
                       console.log('Notification preference saved successfully.');
                   },
                   error: function(xhr, status, error) {
                       console.error('Failed to save notification preference: ' + error);
                   }
               });
           }
       }
   );
</script>

