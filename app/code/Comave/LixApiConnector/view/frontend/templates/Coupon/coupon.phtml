<?php
$couponlist = $block->getCouponlistDetails();
?>
<div class="coupon-list-container">
	<div class="coupons-list row">
		<?php 
            if (isset($couponlist['success']) && $couponlist['success'] == 1) {
				$couponData = $couponlist['data']['data'];
                foreach($couponData as $item){ 
                   ?>
                   <div class="coupons col-lg-3 col-md-4 col-sm-6 mb-3" id="coupon-details">
                        <div class="coupon-grid">
    	                   	<div class="offer-img-container">
    	                    	<img src="<?php echo $item['market']['offer_image']; ?>">
    	                    </div>
    	                    <div class="description"><?php echo $item['description']; ?></div>
                        </div>
                   </div>

                   <?php
                   $couponDetails = $block->getCouponDetails($item['id']);
                   ?>
                   <!-- Coupon container -->
                   	<?php 
			            if (isset($couponDetails['success']) && $couponDetails['success'] == 1 && isset($couponDetails['data'])) {
						?>
                        <div class="coupon-details-popup-container">
        					<div class="coupon-details-container" id="coupon-details-popup">
        						<span class="coupon-details-popup-close">&times;</span>
        						<div class="coupon-body row">
        							<div class="coupon-desc"><?php echo $couponDetails['data']['description']; ?></div>
                                    <div class="coupon-url">
                                        <div class="coupon-url-link">
                                            <a id="couponLink" href="<?php echo $couponDetails['data']['coupon']; ?>" target="_blank">
                                                <?php echo $couponDetails['data']['coupon']; ?>                                        
                                            </a>
                                        </div>
                                        <div class="copy-button">                                        
                                            <button class="copy-coupon-url" onclick="copyCouponLink('<?php echo $couponDetails['data']['coupon']; ?>')"><i class="icon-line-stack-2"></i></button>
                                        </div>
                                    </div>
                                    <div class="show-qrcode">
                                        <button class="coupon-qrcode">Show QR Code</button>
                                        <button class="hide-coupon-qrcode" style="display: none;">Hide QR Code</button>
                                        <div>
                                            <?php 
                                            $qr="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=".$couponDetails['data']['coupon'];
                                            ?>
                                            <img class="coupon-qrcode-img" src="<?php echo $qr; ?>" alt="QR Code" style="display: none;">
                                        </div>
                                    </div>
                                    <div class="show-barcode">
                                        <button class="coupon-barcode">Show Barcode</button>
                                        <button class="hide-coupon-barcode" style="display: none;">Hide Barcode</button>
                                        <div>
                                            <img class="coupon-barcode-img" src="data:image/png;base64,<?php echo $couponDetails['data']['coupon_barcode']; ?>" alt="Barcode" style="display: none;">
                                        </div>
                                    </div>
        						</div>
        					</div>
                        </div>
					<?php
					}				
                }
            } else {
                echo "No coupons available";
            }
        ?>
	</div>
</div>

<script type="text/javascript">
function copyCouponLink(couponUrl) {
    var tempInput = document.createElement("input");
    tempInput.value = couponUrl;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);
}

require(['jquery'], function ($) {
	$(document).ready(function () {
		$('.coupons').each(function(index) {
        	var details = $(this);
            details.click(function(e) {
                e.preventDefault();
                var newClass = 'mycoupon-preview';
                document.body.classList.add(newClass);
                details.next('.coupon-details-popup-container').show();
            });
        });
        $('.coupon-details-popup-close').click(function(e) {
            e.preventDefault();
            var newClass = 'mycoupon-preview';
            if (document.body.classList.contains(newClass)) {
                document.body.classList.remove(newClass);
            }
            var container = $(this).closest('.coupon-details-popup-container');
            container.hide();
            // Reset QR Code and Barcode visibility
            container.find('.coupon-qrcode-img').hide();
            container.find('.coupon-barcode-img').hide();
            container.find('.coupon-qrcode').show();
            container.find('.hide-coupon-qrcode').hide();
            container.find('.coupon-barcode').show();
            container.find('.hide-coupon-barcode').hide();
        });

        $(document).on('click', '.coupon-qrcode', function() {
            var qrCodeImg = $(this).closest('.coupon-body').find('.coupon-qrcode-img');
            qrCodeImg.css('display', 'inline-block');
            $(this).siblings('.hide-coupon-qrcode').show();
            $(this).hide();
        });

        $(document).on('click', '.hide-coupon-qrcode', function() {
            var qrCodeImg = $(this).closest('.coupon-body').find('.coupon-qrcode-img');
            qrCodeImg.hide();
            $(this).siblings('.coupon-qrcode').show();
            $(this).hide();
        });

        $(document).on('click', '.coupon-barcode', function() {
            var barcodeImg = $(this).closest('.coupon-body').find('.coupon-barcode-img');
            barcodeImg.css('display', 'inline-block');
            $(this).siblings('.hide-coupon-barcode').show();
            $(this).hide();
        });

        $(document).on('click', '.hide-coupon-barcode', function() {
            var barcodeImg = $(this).closest('.coupon-body').find('.coupon-barcode-img');
            barcodeImg.hide();
            $(this).siblings('.coupon-barcode').show();
            $(this).hide();
        });
    });
});
</script>