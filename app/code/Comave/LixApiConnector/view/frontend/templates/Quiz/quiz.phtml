<?php
    $quizDataarray = $block->getQuizData();
    $quizData = isset($quizDataarray['quiz']) ? $quizDataarray['quiz'] : '';
    $quizTimer = !empty($quizData) && isset($quizData[0]['quiz_timer']) ? $quizData[0]['quiz_timer'] : '90';
?>

<div class="Main-section">
    <div id="time-up-modal" class="modal">
        <div class="modal-content">
        <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/time-out.png'; ?>" alt="">
            <p id="modal-message">Looks like the clock ran out before you could finish the quiz.<br> Don't worry,
                you
                can always try again tomorrow!</p>
            <button type="button" class="reward-button">See my LIX wallet</button>
        </div>
    </div>
    <?php if (!empty($quizData)): ?>
        <div id="leave-quiz-modal" class="modal">
                <div class="modal-content">
                    <p id="modal-message">Are you want to leave quiz?</p>
                    <button id="yes-button">Yes</button>
                    <button id="cancel-button">Cancel</button>
                </div>   
        </div>
    <?php endif; ?> 
       
        <div id="leave-quiz-modal-2" class="modal">
                <div class="modal-content">
                    <p id="modal-message">Are you want to leave quiz?</p>
                    <button id="yes-button-2">Yes</button>
                    <button id="cancel-button-2">Cancel</button>
                </div>   
        </div>
   
            
    <div class="container-main">
        <section class="top-section">
            <div class="leave" id="leave"><i class="icon-line-arrow-left"></i><button id="leave-quiz-button" class="leave-quiz">Leave Quiz</button>
            </div>
            <div class="quizTime">
                <span class="watch"><i class="icon-time"></i></span>
                <span><span class="time" id="time">0:00</span></span>
            </div>
        </section>
    
        <div class="quiz-container-timer">
            <?php if (!empty($quizData)): ?>
                <h1>Quiz starts in</h1>
                <div class="timer animatable">
                    <svg>
                        <circle cx="50%" cy="50%" r="90" />
                        <circle cx="50%" cy="50%" r="90" pathLength="1" />
                        <text x="100" y="120" text-anchor="middle" class="timer-count">

                            <tspan id="timeLeft"></tspan>
                        </text>
                    </svg>
                </div>
                <?php else: ?>
                <p>You already submitted the quiz.</p>
            <?php endif; ?>   
        </div>

        <div class="quiz-task">
            <div class="card-back-1">
                <div class="card-back-2">
                    <div class="main-card">
                        <?php if (!empty($quizData)): ?>
                            <form id="quiz-form" action="<?php echo $block->getUrl('lixreward/quiz/submit'); ?>" method="POST">
                                <input type="hidden" name="correct_count" id="correct-count" value="0">
                                <?php foreach ($quizData as $index => $quiz): ?>
                                    <div class="quiz-question" id="question_<?php echo $index; ?>" style="<?php echo $index === 0 ? 'display: block;' : 'display: none;'; ?>">
                                        <input type="hidden" name="answer_<?php echo $quiz['question_id']; ?>" value="0">
                                        <img src="<?php echo $quiz['quiz_image']; ?>">
                                        <span class="question-count"> Question <?php echo $index + 1; ?>/<?php echo count($quizData); ?></span>
                                        <h1 class="main-question"><?php echo $quiz['question']; ?></h1>
                                        <?php foreach ($quiz['answers'] as $answer): ?>
                                            <fieldset class="answer-fieldset">
                                                <label>
                                                    <input type="hidden" name="task_id" value="<?php echo $quizDataarray['taskid']; ?>"/>
                                                    <input type="hidden" name="quiz_id<?php echo $quiz['question_id']; ?>" value="<?php echo $quiz['quiz_id']; ?>"/>
                                                    <input type="hidden" name="question_id<?php echo $quiz['question_id']; ?>" value="<?php echo $quiz['question_id']; ?>">
                                                    <ul>
                                                        <li>
                                                            <input type="radio" id="answer_<?php echo $answer['answer_id']; ?>" name="answer_<?php echo $quiz['question_id']; ?>" value="<?php echo $answer['answer_id']; ?>">
                                                            <label  for="answer_<?php echo $answer['answer_id']; ?>" class="answerlabel"><?php echo $answer['answer'];?></label>
                                                        </li>
                                                    </ul>
                                                </label><br/>
                                            </fieldset>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endforeach; ?>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($quizData)): ?>
            <button type="button" class="next-button" id="next-button">Next</button>
            <button type="button" id="quiz-submit-button" style="display: none;">Submit</button>
        <?php endif; ?>

        <div class="congratulations">
            <div class="card-back-1">
                <div class="card-back-2">
                    <div class="main-card">
                        <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/congrats-quiz.png'; ?>" alt="">
                        <h1 class="quiz-congrats">Congratulations!</h1>
                        <P class="quiz-earnedtext">You got <span id="answerdata"></span> correct answers </P>
                        <span class="quiz-reward"><span id="score">+</span> <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/lix_image.png'; ?>" alt="lix"></span>
                        <div class="quiz-infom-main">
                            <div class="quiz-discount">
                                <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/congrats-discount.png'; ?>" alt="">
                                <span><b>Discounts galore! -</b> Apply your LIX Rewards at checkout to save on
                                    almost anything in our store.</span>
                            </div>
                            <div class="quiz-text-reward">
                                <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/congrats-reward.png'; ?>" alt="">
                                <span><b>Every Rewards counts! </b>The more you play Quiz Time, the more rewards you
                                    rack up for bigger discounts.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="reward-button">See my LIX wallet</button>
        </div>

        <div class="score-card">
            <div class="card-back-1">
                <div class="card-back-2">
                    <div class="main-card">
                        <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/score-quiz-new.png'; ?>" alt="">
                        <h1 class="quiz-score-rewards"></h1>
                        <h1 class="quiz-score">Almost there!</h1>
                        <P class="quiz-answercount">You missed <span id="wrongans"></span>  out <span id="total-questions"></span> questions today. </P>
                        <div class="quiz-score-main">
                            <p>Don't sweat it, you can always sharpen your <br>
                                Copa America knowledge and try again tomorrow.</p>
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="reward-button">See my LIX wallet</button>
        </div>
        <div id="loader" style="display: none;">
            <div class="loader-spinner"></div>
        </div>
</div>

<script>
    let timeLeft = 5;
    let timer = document.getElementById('timeLeft');
    let timerElement = document.querySelector('.timer');
    let quizTask = document.querySelector('.quiz-task');
    let quizContainerTimer = document.querySelector('.quiz-container-timer');
    let modalContent = document.getElementById('time-up-modal');
    let modalContentleave = document.getElementById('leave-quiz-modal');
    let modalContentleave2 = document.getElementById('leave-quiz-modal-2');
    let correctCount = 0;
    let countdownTimer; 
    let modalTimer; 
    let nextButton = document.getElementById('next-button');
    let actionTriggered = false; 
    let congratulationsEl = document.querySelector(".congratulations");
    let scoredisplay = document.querySelector(".score-card");
    let submitButton = document.getElementById('quiz-submit-button');
    let quiztimer = document.querySelector('.quizTime');
    let isLeaveModalVisible = false; 
    var quiztime = <?php echo json_encode($quizTimer); ?>;
    var tminutes = Math.floor(quiztime / 60);
    var tseconds = quiztime % 60;
    var formattedTime = tminutes.toString().padStart(2, '0') + ':' + tseconds.toString().padStart(2, '0');
    document.getElementById('time').textContent = formattedTime;
    var questionindex = 0;

    document.addEventListener('DOMContentLoaded', function() {
        const questions = document.querySelectorAll('.quiz-question');
        let currentQuestionIndex = 0;

        nextButton.addEventListener('click', function() {
            const currentQuestion = questions[currentQuestionIndex];
            const nextQuestionId = currentQuestionIndex + 1;
            const nextQuestion = document.getElementById(`question_${nextQuestionId}`);

            currentQuestion.style.display = 'none';
            if (nextQuestion) {
                nextQuestion.style.display = 'block';
                currentQuestionIndex++;

                if (currentQuestionIndex === questions.length - 1) {
                    submitButton.style.display = 'block';
                    nextButton.style.display = 'none'; 
                    questionindex =1;
                }
            }
        });
    });

    function isTimeLeft() {
        return timeLeft > -1;
    }

    function runTimer() {
        const timerCircle = timerElement.querySelector('svg > circle + circle');
        document.getElementById('leave-quiz-button').disabled = true;
        document.getElementById('next-button').disabled = true;
        timerElement.classList.add('animatable');
        timerCircle.style.strokeDashoffset = 1;
        countdownTimer = setInterval(function () {
            if (isTimeLeft()) {
                const timeRemaining = timeLeft--;
                const normalizedTime = (5 - timeRemaining) / 5;
                timerCircle.style.strokeDashoffset = normalizedTime;
                timer.innerHTML = timeRemaining;
                document.getElementById('next-button').disabled = false;
            } else {
                clearInterval(countdownTimer);
                timerElement.classList.remove('animatable');
                timerElement.style.display = 'none';
                quizContainerTimer.style.display = 'none';
                quizTask.style.display = 'flex';
                nextButton.style.display = 'block';
                document.getElementById('leave-quiz-button').disabled = false;
                startModalTimer();
            }
        }, 1000);
    }

     
    window.addEventListener('blur', function() {
            modalContentleave2.style.display = "flex";
    });

    window.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && modalContentleave.style.display === "block") {
                document.getElementById('yes-button').click();
            }
    });

    function startModalTimer() {
        var timeeQuiz = quiztime;
        var quizTime = document.getElementById('time');
        modalTimer = setInterval(function () {
            if (timeeQuiz <= 0) {
                clearInterval(modalTimer);
                if (!actionTriggered) {
                    actionTriggered = true; 
                    clearFormData();
                    var formData = jQuery("#quiz-form").serialize(); 
                    console.log(formData);
                    jQuery.ajax({
                        url: '<?php echo $block->getUrl('lixreward/quiz/submit'); ?>',
                        type: 'POST',
                        data: formData, 
                        dataType: 'json',
                        success: function(response) {
                            console.log(response);
                        },
                        error: function(xhr, status, error) {
                            console.error('Form submission failed:', error);
                        }
                    });
                }
                
                if (isLeaveModalVisible) {
                    modalContentleave.style.display = 'none';
                    isLeaveModalVisible = false;
                }
                scoredisplay.style.display = 'none';
                congratulationsEl.style.display = 'none';
                modalContent.style.display = 'flex';     
            } else {
                timeeQuiz--;
                 let minutes = Math.floor(timeeQuiz / 60);
                 let seconds = timeeQuiz % 60;
                 quizTime.textContent = `${minutes}min ${seconds}s`;
            }
        }, 1000);
    }


    function clearFormData() {
        let inputs = document.querySelectorAll('#quiz-form input[type="radio"]');
        inputs.forEach(input => {
            input.checked = false;
        });
    }

    document.getElementById('quiz-submit-button').addEventListener('click', function() {
        //document.getElementById('quiz-submit-button').disabled = true;
        //let submitButton = document.getElementById('quiz-submit-button');
        //submitButton.style.display = 'none';
       clearInterval(countdownTimer); 
        clearInterval(modalTimer); 
        submitQuizForm(); 
    });

    function submitQuizForm() {
        callControllerAction();
    }

    function callControllerAction() {
        let leavequiz = document.getElementById('leave');
        nextButton.style.display = 'none';
        quiztimer.style.display = 'none';
        leavequiz.style.display = 'none';
        document.getElementById('quiz-submit-button').disabled = true;
        var formData = jQuery("#quiz-form").serialize(); 
        console.log(formData);
        if (!actionTriggered) {
            document.getElementById('loader').style.display = 'flex';
            jQuery.ajax({
                url: '<?php echo $block->getUrl('lixreward/quiz/submit'); ?>',
                type: 'POST',
                data: formData, 
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    let data = {
                        points : response.points,
                        answer : response.correct_answer,
                        question : response.total_questions
                    }
                    document.getElementById('loader').style.display = 'none';
                    if (data.answer == data.question){
                        showSuccess(data);
                    }else{
                        showScore(data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Form submission failed:', error);
                     document.getElementById('loader').style.display = 'none';
                    alert('Failed');
                }
            });
        }
        
        actionTriggered = true;
    }

    function showSuccess(data) {
        quizTask.style.display = 'none';
        congratulationsEl.style.display = "flex";
        nextButton.style.display = 'none';
        submitButton.style.display = 'none';
        quiztimer.style.display = 'none';
        scoredisplay.style.display = 'none';
        document.getElementById("answerdata").textContent = data.answer;
        document.getElementById("score").textContent = data.points;
    }

    function showScore(data) {
        scoredisplay.style.display = 'flex';
        quizTask.style.display = 'none';
        congratulationsEl.style.display = "none";
        nextButton.style.display = 'none';
        submitButton.style.display = 'none';
        quiztimer.style.display = 'none';
        congratulationsEl.style.display = 'none';
        document.getElementById("wrongans").textContent = data.question - data.answer;
        document.getElementById("total-questions").textContent = data.question;

        document.querySelector(".quiz-score-rewards").textContent = `You have earned ${data.points} LIX rewards!`;
    }

    document.getElementById('cancel-button').addEventListener('click', function() {
        modalContentleave.style.display = 'none';  
        isLeaveModalVisible = false;              
        document.getElementById('leave-quiz-button').disabled = false;
        var timeeQuiz = parseInt(document.getElementById('time').textContent.split('min')[0]) * 60 
                    + parseInt(document.getElementById('time').textContent.split('min')[1].split('s')[0]);
        if (timeeQuiz > 0) {
            if(questionindex == 1){
                submitButton.style.display = 'block';
            }else{
                nextButton.style.display = 'block';
            }
        } else {
            nextButton.style.display = 'none';
            submitButton.style.display = 'none';
        }
    });
    
    document.getElementById('cancel-button-2').addEventListener('click', function() {
        modalContentleave2.style.display = 'none';  
        isLeaveModalVisible = false;              
        document.getElementById('leave-quiz-button').disabled = false;
        var timeeQuiz = parseInt(document.getElementById('time').textContent.split('min')[0]) * 60 
                    + parseInt(document.getElementById('time').textContent.split('min')[1].split('s')[0]);
    });

    document.getElementById('yes-button').addEventListener('click', function() {
        clearInterval(countdownTimer); 
        clearInterval(modalTimer);
        document.getElementById('yes-button').disabled = true;
        clearFormData();
        clearInterval(countdownTimer); 
        var formData = jQuery("#quiz-form").serialize(); 
        console.log(formData);
        if (!actionTriggered) {
            jQuery.ajax({
                url: '<?php echo $block->getUrl('lixreward/quiz/submit'); ?>',
                type: 'POST',
                data: formData, 
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    window.location.href = '<?php echo $block->getUrl('lixreward/gamification/lixgamification?countries=231/'); ?>';
                },
                error: function(xhr, status, error) {
                    console.error('Form submission failed:', error);
                }
            });
            actionTriggered = true;
        }else{
            window.location.href = '<?php echo $block->getUrl('lixreward/gamification/lixgamification?countries=231/'); ?>';
        }
    });

    document.getElementById('leave-quiz-button').addEventListener('click', function() {
        document.getElementById('leave-quiz-button').disabled = true;
        clearInterval(countdownTimer); 
        modalContentleave.style.display = 'flex';
        isLeaveModalVisible = true; 
        scoredisplay.style.display = 'none';
        quizTask.style.display = 'block';
        congratulationsEl.style.display = "none";
        nextButton.style.display = 'none';
        submitButton.style.display = 'none';
        congratulationsEl.style.display = 'none';
    });

    document.querySelectorAll('.reward-button').forEach(button => {
            button.addEventListener('click', handleRewardButtonClick);
    });

    function handleRewardButtonClick() {
        console.log("Reward button clicked!"); 
        window.location.href = '<?php echo $block->getUrl('lixreward/account/lixdashboard/'); ?>';
    }
    enableKeyBlocking();
    var timeoutDuration = (quiztime + 5) * 1000;
    setTimeout(disableKeyBlocking, timeoutDuration); 
    runTimer();

    document.addEventListener('keydown', function(event) {
        const key = event.key.toLowerCase();
        if (
        (event.metaKey && key === 'r') || 
        (event.ctrlKey && key === 'r') || 
        (event.ctrlKey && event.shiftKey && key === 'r') ||
        key === 'f5'
        ) {
            event.preventDefault();
            console.log('Key combination blocked.');
        }
    });

    function enableKeyBlocking() {
        document.addEventListener('keydown', blockKeys);
    }

    function disableKeyBlocking() {
        document.removeEventListener('keydown', blockKeys);
    }

    function blockKeys(event) {
        if ((event.metaKey || event.ctrlKey) && event.key === 'r') {
            event.preventDefault();
            console.log('Key combination blocked.');
        }
    }

</script>
