<?php
try {
    $verifyLinkResponse = json_decode($block->verifyLink(), true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Error decoding verifyLink response: ' . json_last_error_msg());
    }

    $points = 0;
    $taskid = 0;
    $nopoints = false;

    if (isset($verifyLinkResponse['success']) && $verifyLinkResponse['success']) {
        $points = $verifyLinkResponse['points'] ?? 0;
        $taskid = $verifyLinkResponse['taskid'] ?? 0;
    } else {
        $taskid = $verifyLinkResponse['taskid'] ?? 0;
        $nopoints = true;
    }

    $rewardOptionResponse = json_decode($block->rewardoption(), true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Error decoding rewardOption response: ' . json_last_error_msg());
    }

    $rewardamount = array();

    if (isset($rewardOptionResponse['success']) && $rewardOptionResponse['success']) {
        $rewardamount = isset($rewardOptionResponse['rewardoptions']) && !empty($rewardOptionResponse['rewardoptions']) 
            ? $rewardOptionResponse['rewardoptions'] 
            : [0, 10, 20, 40, 30];
    } else {
        $nopoints = true;
        if (isset($rewardOptionResponse['rewardnulloptions'])) {
            $rewardamount = isset($rewardOptionResponse['rewardnulloptions']) && !empty($rewardOptionResponse['rewardnulloptions']) 
                ? $rewardOptionResponse['rewardnulloptions'] 
                : [0, 10, 20, 40, 30];
        } else {
            $rewardamount = [0, 10, 20, 40, 30];
        }
    }

    $length = count($rewardamount);
	

} catch (Exception $e) {
    // Log the error or handle it accordingly
    error_log($e->getMessage());
    $taskInactive = true;
}

if (isset($taskInactive) && $taskInactive) {
    echo '<div class="error-message">This task is temporarily inactive. Please try again later.</div>';
} else {
?>
    <div class="page-main-spin">
        <div class="left-section">
            <h1 class="heading-top">Spin the Wheel</h1>
            <p class="text-top">Get lucky and give it a spin and get a fortune.</p>
        </div>
        <div class="right-section">
            <img src="<?php echo htmlspecialchars($block->getMediaUrl() . 'wysiwyg/gamification/spinwheel-image.png', ENT_QUOTES, 'UTF-8'); ?>" alt="spin wheel">
        </div>
    </div>
    <div class="wheel-main-outer">
        <div class="main-spin">
            <div id="reward-display">
                <!-- <p>Spin each day for a chance to win prizes!</p> -->
                <h4 class="spin-title">Click on ‘Spin now’ to get the rewards</h4>
            </div>
            <div id="wheel-pointer"></div>
            <div class="wheel-container">
                <div id="wheel" class="wheel wheel-count-<?php echo $length; ?>">
                </div>
                <img src="<?php echo htmlspecialchars($block->getMediaUrl() . 'wysiwyg/gamification/wheel_image.png', ENT_QUOTES, 'UTF-8'); ?>" class="brand logo">
            </div>
            <button id="spin-btn">Spin now</button>
            <button id="show-rewards-btn" style="display: none;">See my LIX wallet</button>
        </div>
        <div class="wheel-half">
        </div>
    </div>
    
<?php
}
?>

<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.4.0/dist/confetti.browser.min.js"></script>
<script>
    let rewards = <?php echo json_encode($rewardamount); ?>;
    var points = <?php echo json_encode($points); ?>;
    var taskid = <?php echo json_encode($taskid); ?>;
    var nopoints = <?php echo json_encode($nopoints); ?>;
    let selectedReward = points;

    document.addEventListener("DOMContentLoaded", () => {
        const wheel = document.getElementById("wheel");
        const spinBtn = document.getElementById("spin-btn");
        const showRewardsBtn = document.getElementById("show-rewards-btn");
        const rewardDisplay = document.getElementById("reward-display");
        // const modal = document.getElementById("reward-modal");
        // const closeBtn = document.getElementsByClassName("close-btn")[0];
        // const rewardMessage = document.getElementById("reward-message");
        let anglePerSegment = 360 / rewards.length;
        let continuousSpinInterval;

        function generateWheel() {
            wheel.innerHTML = '';
            for (let i = 0; i < rewards.length; i++) {
                const segment = document.createElement('div');
                segment.classList.add('segment');
                segment.style.transform = `rotate(${i * anglePerSegment}deg) skewY(${90 - anglePerSegment}deg)`;
                segment.innerHTML = `<div class="inner-segment segment-count-<?php echo $length; ?>">
                    <span class="text-point">Rewards</span>
                    <span class="lix-points" style="position: relative;">${rewards[i]}</span>
                    <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/segment_image.png'; ?>" />
                </div>`;
                wheel.appendChild(segment);
            }
            if (nopoints) {
                rewardDisplay.innerHTML = `<span class="lix-rewardmsg">This spin the wheel has already been claimed.</span>`;
                document.getElementById('spin-btn').disabled = true;
                showRewardsBtn.style.display = 'none';
            }
        }
        
        function startContinuousSpin() {
            let angle = 0;
            continuousSpinInterval = setInterval(() => {
                angle += 20;
                wheel.style.transform = `rotate(${angle}deg)`;
            }, 50);
        }

        function stopContinuousSpin() {
            clearInterval(continuousSpinInterval);
        }

        function spinWheel() {
            const spins = 10; 
            const selectedIndex = rewards.indexOf(selectedReward.toString());
            const stopAngle = 360 - selectedIndex * anglePerSegment - anglePerSegment / 2; 
           // const randomOffset = Math.random() * anglePerSegment - anglePerSegment / 2; 
            const totalAngle = spins * 360 + stopAngle + anglePerSegment; 


            wheel.style.transform = `rotate(${totalAngle}deg)`;
            
            setTimeout(() => {
                const earnedLixImageUrl = "<?php echo $block->getMediaUrl() . 'wysiwyg/gamification/earned-lix.png'; ?>";
                if (selectedReward > 0) {
                    rewardDisplay.innerHTML = `<span class="lix-rewardmsg">Congratulations! You have Earned</span><br> <span> <img id="earncoinss" src="${earnedLixImageUrl}" alt="earned-lix">  ${selectedReward} LIX Rewards</span>`;
                } else {
                    rewardDisplay.innerHTML = `<span class="lix-rewardmsg">OOPS! Better Luck Next Spin</span><br> <span> ${selectedReward} LIX Rewards </span>`;
                }
                confetti();
                spinBtn.style.display = 'none'; 
                showRewardsBtn.style.display = 'inline';                 
            }, 4000); 
        }

        spinBtn.addEventListener("click", function() {
            document.getElementById('spin-btn').disabled = true;
          //  startContinuousSpin();
            if (points > 0) {
                callBlockFunction();
            } else {
                setTimeout(() => {
                    stopContinuousSpin();
                    spinWheel();
                }, 2000); 
            }
        });

        showRewardsBtn.addEventListener("click", function() {    
            window.location.href = '<?php echo $block->getUrl('lixreward/account/lixdashboard/'); ?>';
        });

        function callBlockFunction() {
            spinWheel();
            jQuery.ajax({
                url: '<?php echo $block->getUrl('lixreward/spinwheel/submit'); ?>',
                type: 'POST',
                data: { task_id: taskid }, 
                dataType: 'json',
                success: function(response) {
                    stopContinuousSpin();
                    if (response.success) {
                        selectedReward = response.points.toString();
                        
                    } else {
                        rewardDisplay.innerHTML = `<span>This task has expired</span>`;
                        console.error('Task submission failed');
                    }
                },
                error: function(xhr, status, error) {
                    stopContinuousSpin();
                    console.error('Task submission failed:', error);
                }
            });
        }
        generateWheel();
    });
</script>
<style>
<?php 
	if($length==3){
	?>
	
	<?php
	}elseif($length==4){
	?>
	
	<?php	
	}elseif($length==5){
	?>
	
	<?php
	}elseif($length==6){
	?>
	
	<?php	
	}elseif($length==7){
	?>
	
	<?php	
	}elseif($length==8){
	?>
	
	<?php	
	}elseif($length==9){
	?>
	
	<?php	
	}elseif($length==10){
	?>
	
	<?php	
	}elseif($length==11){
	?>
	
	<?php	
	}
?>
</style>