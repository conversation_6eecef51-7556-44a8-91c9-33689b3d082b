<!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> -->
<div class="update-pin-details">
    <form id="updatepin-form" action="<?php echo $block->getOtpDetails(); ?>" method="post">
        <div class="pin">
            <label>Enter Your LIX Pay Pin</label>
            <div class="password-wrapper">
                 <input type="password" id="pin" name="pin">
                 <span class="toggle-password"><i class="fa fa-eye"></i></span>
            </div>
        </div>
        <div class="reenter-pin">
           <label>Re-Enter Your LIX Pay Pin</label>
           <div class="password-wrapper">
                <input type="password" id="reenter_pin" name="reenter_pin">
                <span class="toggle-password"><i class="fa fa-eye"></i></span>
            </div>
        </div>        
        <div class="otp-field" style="display: none;">
           <label>Enter OTP</label>
           <input type="text" id="otp" name="otp" >
        </div>
        <!-- Loader element  -->
        <div id="loader" style="display: none;">
            <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>" alt="Loading...">
        </div>
        <div id="error-message"></div>
        <div id="success-message"></div>
        <div class="submit-pin">
            <button>Submit</button>
        </div>
    </form>
</div>

<script type="text/javascript">    
    require(['jquery'], function($) {
        $(document).ready(function() {            
             $('#updatepin-form').on('submit', function (e) {
                $('#error-message').html('');
                $('#success-message').html('');
                e.preventDefault();
                var form = $(this);
                var pin = $('#pin').val();
                var reenterPin = $('#reenter_pin').val();
                
                // Check if the entered PIN and re-entered PIN are the same
                if(pin !== reenterPin) {
                    document.getElementById("error-message").innerHTML = "Entered PIN and Re-entered PIN does not match!";
                    return; 
                }
                if(!/^\d{4}$/.test(pin)) {
                    document.getElementById("error-message").innerHTML = "PIN must be a 4-digits Numbers.";
                    return;
                }
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    dataType: 'json',
                    beforeSend: function() {
                        $('#loader').show();
                    },
                    success: function (response) {                         
                        if (response.status === 'success') {
                           document.querySelector('.otp-field').style.display = 'block';  
                            form.off('submit').on('submit', function(e) {
                                e.preventDefault(); 
                                var pin = $('#pin').val();
                                var otp = $('#otp').val();
                                $.ajax({
                                    type: form.attr('method'),
                                    url: form.attr('action'), 
                                    data: { 
                                        otp: otp,
                                        pin: pin 
                                    },
                                    dataType: 'json',
                                    beforeSend: function() {
                                        $('#loader').show();
                                    },
                                    success: function (response) {
                                        if (response.status === 'success') {
                                            document.getElementById("success-message").innerHTML = "LIX Pay Pin Updated Successfully.";
                                        } 
                                        else {
                                            document.getElementById("error-message").innerHTML = "Invalid OTP.";
                                        } 
                                                        
                                    },
                                    error: function (xhr, status, error) {
                                        console.error("XHR status:", status);
                                        console.error("Error:", error);
                                        console.error("Response:", xhr.responseText);
                                    },
                                    complete: function() {
                                        // Hide loader after request completes (success or error)
                                        $('#loader').hide();
                                    }
                                });
                            });
                        } else {
                            document.getElementById("error-message").innerHTML = "Unable to send OTP.";
                        }                     
                        
                    },
                    error: function (xhr, status, error) {
                           console.error("XHR status:", status);
                            console.error("Error:", error);
                            console.error("Response:", xhr.responseText);
                    },
                    complete: function() {
                        // Hide loader after request completes (success or error)
                        $('#loader').hide();
                    }
                });
            });           
        });
    });

     document.querySelectorAll('.toggle-password').forEach(function(icon) {
        icon.addEventListener('click', function() {
            var inputField = icon.previousElementSibling;
            if (inputField.type === 'password') {
                inputField.type = 'text';
                icon.querySelector('i').classList.remove('fa-eye');
                icon.querySelector('i').classList.add('fa-eye-slash');
            } else {
                inputField.type = 'password';
                icon.querySelector('i').classList.remove('fa-eye-slash');
                icon.querySelector('i').classList.add('fa-eye');
            }
        });
    });
</script>