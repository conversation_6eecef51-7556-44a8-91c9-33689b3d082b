<?php
$sessionManager = $block->getSessionManager(); 
$currentTime = time();
$expiryTimeInSeconds = 300;
$remainingTime = $expiryTimeInSeconds;
$balanceDetails = $block->getBalanceDetails();
?>
<script type="text/javascript">
    function displayCountdown(remainingTimeInSeconds) {
                
                var interval = setInterval(function() {
                if (remainingTimeInSeconds <= 0) {
                    clearInterval(interval);
                    //document.getElementById("qr-code").innerHTML = "Please generate QR code again";
                    document.getElementById("qr-code-uuid").innerHTML = " ";
                    document.getElementById("qr-code-container").innerHTML = " ";
                    document.getElementById("message-container").innerHTML = " ";
                    document.getElementById("conv-container").innerHTML = " ";
                    document.getElementById("rem-container").innerHTML = " ";
                    document.getElementById("barcode").style.display = "none";
                    document.getElementById("show-barcode-btn").style.display = "none";
                    document.getElementById("countdown").innerHTML = "QR code expired. Please generate another QR code";
                }
                else{
                    var hours = Math.floor(remainingTimeInSeconds / 3600);
                    var minutes = Math.floor((remainingTimeInSeconds % 3600) / 60);
                    var seconds = remainingTimeInSeconds % 60;

                    // Format hours, minutes, and seconds
                    var formattedTime = minutes.toString().padStart(2, '0') + " minutes : " +
                                        seconds.toString().padStart(2, '0') + " seconds remaining";
                   // alert("formattedTime");
                    document.getElementById("countdown").innerHTML = formattedTime;
                    remainingTimeInSeconds--;
                }
            }, 1000);
        }
</script>
<p><?= $block->escapeHtml(__('Please enter the amount of money you wish to pay with ')) ?>
<?php if ($balanceDetails['data']) {
   echo $balanceDetails['data']['currencyuser']['symbol']; 
} ?></p>

<div class="qr-details">
    <form id="payment-form" action="<?php echo $block->getSubmitActionUrl('lixreward/lixpay/submit'); ?>" method="post">
        <div class="paymentinnr">
            <div class="payinput" id="payinput">
                <input type="text" id="amount" name="amount" required>
                <input type="hidden" id="currency" name="currency" value="<?= $balanceDetails['data']['currencyuser']['symbol'] ?>">
            </div>
            <div class="droppay">
                <select id="currency_field" name="currency_field" >
                    <option value="USD">USD</option>
                    <option value="GBP">GBP</option>           
                    <option value="QAR">QAR</option>
                </select>
            </div>
            </div>
            <div id="rem-bal-container"><span id="rem-container"></span>
                
            </div>

        <div class="genratebtn">
            <button id="submit-btn" class="generate-qr">Generate QR code</button>
        </div>
    </form> 
    <div id="loader" style="display: none;">
    <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>"
                        alt="Loading...">
</div>
    <div class="qr-code" id="qrcode" style="display:none;">
        <p class="qr-code-head">Please ask the cashier to scan the QR to place the payment</p>
       <div id="qr-code-container">
     <?php 
             $currentTime = time();
             $savedexpTime = $sessionManager->getCurrTime() + (5 * 60);
             $timeDifference = $savedexpTime - $currentTime;

                if ($timeDifference > 0) {
                    // Display the stored QR code data if available
                    $qrResponse = $sessionManager->getQrResponse();
                     if (is_array($qrResponse)) {
                          echo $qrResponse['qr'];
                    }
                }
            ?>
</div>

        <div id="countdown">
            <?php
            if ($timeDifference <= 0 || $timeDifference > 300) {
                $sessionManager->unsCurrTime();
                $sessionManager->unsQrResponse();
            }
            if ($timeDifference > 0) {
                echo '<script type="text/javascript">';
                echo 'displayCountdown(' . $timeDifference . ');';
                echo '</script>';
            } 
            ?>
        </div>
         <div class="conv-text">
             <p>Exchange rate is</p>       
            <div id="conv-container"></div>
            <?php
                $currentTime = time();
                $savedexpTime = $sessionManager->getCurrTime() + (5 * 60);
                $timeDifference = $savedexpTime - $currentTime;
                if ($timeDifference > 0) {
                    // Display the stored uuid code data if available
                    $convResponse = $sessionManager->getConvResponse();
                     if ($convResponse) {
                        echo $convResponse;
                    }
                }
            ?>
        </div>
        <div class="barcode-container">            
             <?php
                 if ($timeDifference > 0) {
                    $qrResponse = $sessionManager->getQrResponse();
                    if (is_array($qrResponse)) {
                        ?>
                            <button id="show-barcode-btn">Show Barcode</button>
                <!-- Add the Hide Barcode button here initially hidden -->
                <button id="hide-barcode-btn" style="display: none;">Hide Barcode</button>
                <div id="barcode">                
                    <img id="barcode-img" src="data:image/png;base64,<?= $qrResponse['barcode'] ?>" alt="Barcode" style="display: none;">   
                </div>
                        <?php
                    }
                }
                else{
                    ?>
                  <button id="show-barcode-btn" style="display: none;">Show Barcode</button>
            <!-- Add the Hide Barcode button here initially hidden -->
            <button id="hide-barcode-btn" style="display: none;">Hide Barcode</button>
            <div id="barcode">                
                <img id="barcode-img" src="" alt="Barcode" style="display: none;">   
                <p class="or"> -- OR -- </p>  
            </div>
                    <?php
                } 
                ?>            
        </div>
        <div class="qr-data">
            <p>Enter UUID manually</p>
            <div id="qr-code-uuid">
            <?php
                $currentTime = time();
                $savedexpTime = $sessionManager->getCurrTime() + (5 * 60);
                $timeDifference = $savedexpTime - $currentTime;
                if ($timeDifference > 0) {
                    // Display the stored uuid code data if available
                    $qrResponse = $sessionManager->getQrResponse();
                     if (is_array($qrResponse)) {
                        echo $qrResponse['uuid'];
                    }
                }
            ?>
            </div>
        </div>
    </div>
</div>

<div id="successMessage" style="display: none;">
    <!-- <p>Transaction successful!</p> -->
    <!-- Add any additional HTML content you want to display after a successful transaction -->
</div>
<!-- Add this div for the pending popup modal -->
 <div id="pending-qr-popup-modal" style="display:none;">
    <div id="pending-qr-popup-content"> 
        <!-- Popup content will be loaded dynamically via AJAX -->
    </div>
    <div id="loader-pending" style="display: none;">
    <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>"
                        alt="Loading...">
    </div>
</div> 
<script type="text/javascript">    
    require([
        'jquery',
        'Magento_Ui/js/modal/modal'
        ], function($, modal) {
        $(document).ready(function() {  
            // $('#submit-btn').on('click', function() { // Bind click event to the button
            //     $('#qrcode').show(); // Show the div with ID 'qrcode'
            // }); 
            $('#payment-form').on('submit', function (e) {
                e.preventDefault();
                var form = $(this);
                $('#loader').show();
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    dataType: 'json',
                    success: function (response) {
                        // Display response message
                        // alert(response);
                        //console.log(response.subtractionResult);
                        $('#qr-code-container').html(response.responseData.qr);
                        $('#qr-code-uuid').html(response.responseData.uuid);
                        $('#barcode-img').attr('src', 'data:image/png;base64,' + response.responseData.barcode);
                        $('#show-barcode-btn').show();
                        $('#qrcode').show();
                        $('#rem-container').html("Remaining Balance: " + response.subtractionResult);
                        $('#conv-container').html(response.conversionRate);
                        $('#message-container').html(response.message);
                        // $('#payinput').html(response.inputAmt);
                       
                        // Display QR code expiration timer
                        var expiryTimeInSeconds = <?php echo $remainingTime; ?>;
                        if (expiryTimeInSeconds > 0) {
                            displayCountdown(expiryTimeInSeconds);
                        } else {
                            document.getElementById("countdown").innerHTML = "QR code expired. Please generate another QR code.";
                           
                            return;
                        }
                          $('#loader').hide();

                    },
                    error: function (xhr, status, error) {
                        $('#loader').hide();
                        //console.error(xhr.responseText);
                    }
                });
            });   

            $(document).ready(function() {    
                $(document).on('click', '#show-barcode-btn', function() {
                    // Show the barcode image
                    $('#barcode-img').css('display', 'inline-block');
                    // Show the "Hide Barcode" button
                    $('#hide-barcode-btn').show();
                    // Hide the "Show Barcode" button
                    $(this).hide();
                });
                
                $(document).on('click', '#hide-barcode-btn', function() {
                    // Hide the barcode image
                    $('#barcode-img').hide();
                    // Show the "Show Barcode" button
                    $('#show-barcode-btn').show();
                    // Hide the "Hide Barcode" button
                    $(this).hide();
                });
            });

            $(document).ready(function() {
                // Function to toggle visibility of remaining balance container
                function toggleRemainingBalanceVisibility() {
                    var amount = $('#amount').val();
                    if (amount !== "") {
                        $('#rem-bal-container').show();
                    } else {
                        $('#rem-bal-container').hide();
                    }
                }

                // Call the function initially to set visibility based on initial input value
                toggleRemainingBalanceVisibility();

                // Call the function whenever there's a change in the input field
                $('#amount').on('input', function() {
                    toggleRemainingBalanceVisibility();
                    // Store the input box value in local storage
                    localStorage.setItem('inputAmount', $(this).val());
                });

                // Retrieve and set the input box value from local storage
                var storedInputAmount = localStorage.getItem('inputAmount');
                if (storedInputAmount) {
                    $('#amount').val(storedInputAmount);
                }
            });

            function setSelectedCurrency() {
            var selectedCurrency = sessionStorage.getItem('selectedCurrency');
                if (selectedCurrency) {
                    document.getElementById('currency_field').value = selectedCurrency;
                }
             }

            // Function to store the selected dropdown value in sessionStorage
            function storeSelectedCurrency() {
                var selectElement = document.getElementById('currency_field');
                var selectedCurrency = selectElement.value;
                sessionStorage.setItem('selectedCurrency', selectedCurrency);
            }

            // Call the function to set the selected dropdown value on page load
            setSelectedCurrency();

            // Add event listener to store the selected dropdown value when it changes
            document.getElementById('currency_field').addEventListener('change', storeSelectedCurrency);
            // Function to handle change in input field value with debounce
            var debounceTimer;
            $('#amount, #currency_field').on('input', function() {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(function() {
                    fetchRemainingBalance();
                }, 500); // Adjust debounce interval as needed
            });

            // Function to fetch remaining balance
            function fetchRemainingBalance() {
                var amount = $('#amount').val();
                var currency = $('#currency_field').val();
                var currency_symbol = $('#currency').val();
                $.ajax({
                    type: 'POST',
                    url: '<?php echo $block->getInputUrl('lixreward/lixpay/RemBalance'); ?>',
                    data: {amount: amount, currency: currency, currency_symbol: currency_symbol},
                    dataType: 'json',
                    success: function(response) {
                        console.log(response.responseData);
                        $('#rem-container').html("Remaining Balance: " + response.responseData);
                        if (response.status === 'insufficient_balance') {
                            $('#submit-btn').prop('disabled', true);
                            alert('Insufficient Balance');
                        } else {
                            $('#submit-btn').prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                    }
                });
            }

            $('#amount, #currency_field').on('change', function() {
                var amount = $('#amount').val();
                var currency = $('#currency_field').val();                
                var currency_symbol = $('#currency').val();
                 //console.log(amount, currency);

                // Make AJAX request to the desired controller
                $.ajax({
                    type: 'POST',
                    url: '<?php echo $block->getInputUrl('lixreward/lixpay/RemBalance'); ?>',
                    data: {amount: amount, currency: currency, currency_symbol: currency_symbol},
                    dataType: 'json',
                    success: function(response) {
                        console.log(response.responseData);
                       
                        if (response.status === 'insufficient_balance') {
                            $('#submit-btn').prop('disabled', true);
                            alert('Insufficient Balance');
                        } else {
                            $('#submit-btn').prop('disabled', false);
                            $('#rem-container').html("Remaining Balance: " + response.responseData);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log(xhr.responseText);
                    }
                });
            }); 

            $(document).ready(function() {
                // Call the controller action initially when the document is ready
                callControllerAction();
                
                setInterval(callControllerAction, 10000);

                function callControllerAction() {
                    $.ajax({
                        type: 'POST',
                        url: '<?php echo $block->getQrStatusUrl('lixreward/lixpay/QrBalance'); ?>',
                        dataType: 'json',
                        success: function(response) {
                            if (response.status === 'approved') {
                                $('#qr-code-uuid').html('');
                                $('#qr-code-container').html('');
                                $('#barcode').hide();
                                $('#show-barcode-btn').hide();
                                $('#countdown').hide();
                                $('#conv-container').hide();
                                $('#message-container').html(response.responseData);
                                $('#qrcode').hide();
                                $('#amount').val(''); // Clear the value of the input field
                                $('#rem-container').hide();
                                $('#successMessage').show();
                                alert('Transaction successful!');

                                location.reload();                               
                            } else if (response.status === 'pending') {
                                $('button.generate-qr').addClass('pending-qr');
                                $('#pending-qr-popup-content').html(response.pendingMsg);
                            }
                            else {
                                //$('#message-container').html(response.responseData);
                            }   
                        },
                        error: function(xhr, status, error) {
                            //console.error(xhr.responseText);
                        }
                    });
                }
            var pendingqroptions = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                title: 'Pending Transaction',
                 buttons: [{
                    text: $.mage.__('Cancel Transaction'),
                    class: 'cancel-button',
                    click: function () { 
                        rejectAction();
                    }
                },{
                    text: $.mage.__('Close'),
                    class: 'close-button',
                    click: function () {
                        closeAction();
                    }
                }]
            };
            var pendingqrpopup = modal(pendingqroptions, $('#pending-qr-popup-modal'));

                $(document).on('click', '.pending-qr', function (e) {
                    e.preventDefault();
                    $('#pending-qr-popup-modal').modal('openModal');
                    return false;
                });
                // reject action
                function rejectAction() {
                    var requestData = {
                        uuid: document.getElementById('qr-code-uuid').innerText
                    };
                    $.ajax({
                        url: '<?php echo $block->getRejectActionUrl('lixreward/lixpay/cancelpending'); ?>', 
                        type: 'POST',
                        dataType: 'json',
                        data: requestData,                        
                        beforeSend: function () {
                            $('#loader-pending').show();
                        },
                        success: function(response){
                            if (response.status === 'success') {
                                $('#pending-qr-popup-modal').modal('closeModal', $('#pending-qr-popup-modal'));
                                if ($(document).find('.pending-qr').length) {
                                    $(document).find('.pending-qr').removeClass('pending-qr');
                                }
                                $('#qr-code-uuid').html('');
                                $('#qr-code-container').html('');
                                $('#barcode').hide();
                                $('#show-barcode-btn').hide();
                                $('#countdown').hide();
                                $('#conv-container').hide();
                                $('#message-container').html(response.responseData);
                                $('#qrcode').hide();
                                $('#amount').val(''); // Clear the value of the input field
                                $('#rem-container').hide();
                                $('#successMessage').show();
                            } else {
                                alert('Failed to cancel transaction.');
                            }
                        },
                        error: function(xhr, status, error) {

                                alert("in reject failure");
                            console.error(xhr.responseText);
                        },
                        complete: function () {
                            $('#loader-pending').hide();
                        }
                    });
                }
                function closeAction() {
                    $('#pending-qr-popup-modal').modal('closeModal');
                    
                }
            });
            
        });
    });

    document.addEventListener("DOMContentLoaded", function() {
        // Check if QR code data exists and display it
        var qrCodeContainer = document.getElementById("qr-code-container");
        if (qrCodeContainer.innerHTML.trim() !== "") {
            // QR code exists, make sure the QR section is visible
            document.getElementById("qrcode").style.display = "block";
            // If there's a valid countdown time, restart the countdown
            var timeDifference = <?= $timeDifference > 0 ? $timeDifference : 0 ?>;
            if (timeDifference > 0) {
                displayCountdown(timeDifference);
            } else {
                // Handle the case where the QR code has expired
                document.getElementById("countdown").innerHTML = "QR code expired. Please generate another QR code.";
                // Clear the input box with id 'amount'
                document.getElementById('amount').value = ''; 
            }
        } else {
            // If QR code does not exist, clear the input box value
            document.getElementById('amount').value = ''; 
        }
    });

    function displayCountdown(remainingTimeInSeconds) {
        var interval = setInterval(function() {
            if (remainingTimeInSeconds <= 0) {
                clearInterval(interval);
                // Hide the QR code container
                document.getElementById("qrcode").style.display = "none";
                document.getElementById('amount').value = ''; 
                var selectBox = document.getElementById('currency_field');

                 selectBox.value = '';
                // Clear the QR code content
                document.getElementById("qr-code-container").innerHTML = "";
                // Reset the countdown message
                document.getElementById("countdown").innerHTML = "QR code expired. Please generate another QR code";
            } else {
                var hours = Math.floor(remainingTimeInSeconds / 3600);
                var minutes = Math.floor((remainingTimeInSeconds % 3600) / 60);
                var seconds = remainingTimeInSeconds % 60;

                // Format hours, minutes, and seconds
                var formattedTime = minutes.toString().padStart(2, '0') + " minutes : " +
                                    seconds.toString().padStart(2, '0') + " seconds remaining";
                document.getElementById("countdown").innerHTML = formattedTime;
                remainingTimeInSeconds--;
            }
        }, 1000);
    }


</script>
