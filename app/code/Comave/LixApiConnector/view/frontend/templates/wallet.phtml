<?php
$details = $block->getWalletlistDetails();
$blockName = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');

$lixblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Lixdashboard');
$walletSymbol = $lixblock->getLixxWalletSymbol();
?>
<p class="notemsg"><?= $block->escapeHtml(__('In order for you to be able to confirm the transaction
    on the POS device, we recommend you to set the authorization pin.')) ?></p>
<div class="general-qr-code-container">
    <div class="general-qr-code row">
        <div class="uuid col-lg-6">
            <div class="uuid-inner">
                <?php 
                    $qrCodeUrl = $block->getQrCode(); 
                ?>
                <?php $wallet = $block->getWallet(); ?>
                <?php 
                    if (isset($wallet['success']) && $wallet['success'] == 1 && !empty($wallet['data'])) {
                        ?>
                        <div class="user-uuid">
                            <?php echo $wallet['data'][0]['created_by_user']['uuid']; ?>
                        </div>
                        <?php
                    }
                    else {
                        echo $block->escapeHtml(__('No wallets are assigned.'));
                    }                
                    if($qrCodeUrl){
                        ?>
                        <img src="<?php echo $qrCodeUrl; ?>" alt="QR Code">
                        <?php
                    }
                ?>                
                <p><?= $block->escapeHtml(__('Please select your preferred LIX wallet from the options below to generate a custom QR code for
                    paying:')) ?></p>
                <?php
                if (isset($details['success']) && $details['success'] == 1 && isset($details['data'])) { ?>
                    <div class="wallet-list">
                        <div class="my-wallets">
                            <?php 
                                if (isset($details['success']) && $details['success'] == 1 && isset($details['data'])) {
                                    foreach ($details['data'] as $detail) {
                                        $balancePageUrl = $block->getBalancePageUrl($detail['currency_symbol']);
                                        if($walletSymbol == $detail['currency_symbol']){
                                            $balanceLessThanOne = ($detail['max_balance'] < 1);
                                        }
                                        else{
                                            $balanceLessThanOne = ($detail['balance'] < 1);
                                        }
                                                                            
                                    ?>
                                         <a href="<?php echo $balanceLessThanOne ? 'javascript:void(0)' : $balancePageUrl; ?>" class="wallets row <?= $balanceLessThanOne ? 'less-than-one-balance' : ''; ?>" data-currency-symbol="<?php echo $detail['currency_symbol']; ?>">
            
                                            <div class="col-lg-2 currency-icon">
                                                <span class="icon-currency-img"><img src="<?php echo $detail['currency_icon']; ?>">
                                                </span>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="currency-name">
                                                    <?= $escaper->escapeHtml($detail['currency_name']); ?>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 customer-balance">
                                                <div class="cust-bal-inner">
                                                    <div class="balance">
                                                        <?php 
                                                        if($walletSymbol == $detail['currency_symbol']){
                                                            echo number_format((float)$detail['max_balance'], 3, '.', '') . " ". $detail['currency_name'];
                                                        }
                                                        else{
                                                            echo $detail['balance'] . " ". $detail['currency_name']; 
                                                        }
                                                        
                                                        ?>
                                                        <span>
                                                            <?php 
                                                                if($walletSymbol == $detail['currency_symbol']){
                                                                    $bal = number_format((float)$detail['max_balance'], 3, '.', '');
                                                                }
                                                                else{
                                                                    $bal = $detail['balance']; 
                                                                }
                                                                $currencyBalance = $bal * $detail['rate_to_usd'];
                                                                if($currencyBalance == 0){
                                                                    ?>
                                                                    <?= $block->escapeHtml(__('~ $'.$currencyBalance)) ?>
                                                                <?php
                                                                }
                                                                else
                                                                {                                                                
                                                                    $currBalance = number_format((float)$currencyBalance, 3, '.', '');
                                                                    ?>
                                                                    <?= $block->escapeHtml(__('~ $'.$currBalance)) ?>
                                                                    <?php
                                                                }
                                                               
                                                            ?>
                                                        </span>
                                                    </div>

                                                </div>
                                            </div>
                                        </a>
                                    <?php
                                    }
                                } else {
                                    echo $block->escapeHtml(__('LIX wallet details not exist.'));
                                }
                            ?>
                        </div>
                    </div>
                <?php } ?>     
            </div>
        </div>
        <div class="qr-code col-lg-6">
            <div class="update-pin-details">
                <form id="updatepin-form" action="<?php echo $block->getOtpDetails(); ?>" method="post">
                    <h2> Update <span> PIN <span></h2>
                    <div class="pin">
                        <label><?= $block->escapeHtml(__('Enter Your LIX Pay Pin')) ?></label>
                        <div class="password-wrapper">
                            <input type="password" id="pin" name="pin">
                            <span class="toggle-password"><i class="fa fa-eye"></i></span>
                        </div>
                    </div>
                    <div class="reenter-pin">
                        <label><?= $block->escapeHtml(__('Re-Enter Your LIX Pay Pin')) ?></label>
                        <div class="password-wrapper">
                            <input type="password" id="reenter_pin" name="reenter_pin">
                            <span class="toggle-password"><i class="fa fa-eye"></i></span>
                        </div>
                    </div>
                    <div class="otp-field" style="display: none;">
                        <label><?= $block->escapeHtml(__('Enter OTP')) ?></label>
                        <input type="text" id="otp" name="otp" maxlength="6">
                    </div>
                    <!-- Loader element  -->
                    <div id="loader" style="display: none;">
                        <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>"
                            alt="Loading...">
                    </div>
                    <div id="error-message"></div>
                    <div id="success-message"></div>
                    <div class="submit-pin">
                        <button><?= $block->escapeHtml(__('Submit')) ?></button>
                    </div>
                </form>
            </div>
        </div>
        <!-- Popup for accounts with balance less than 1 -->
        <div id="less-than-one-balance-popup" style="display: none;">
            <div class="popup-content">
                <p>No Balance in your wallet please earn</p>
            </div>
        </div>
    </div>
</div>
<!-- Add this div for the popup modal -->
 <div id="popup-modal" style="display:none;">
    <div id="popup-content"> 
        <!-- Popup content will be loaded dynamically via AJAX -->
    </div>
    <div id="popup-conRate"> 
        <!-- Popup content will be loaded dynamically via AJAX -->
    </div>
</div> 

<!-- Add this div for the popup modal -->
 <div id="pending-popup-modal" style="display:none;">
    <div id="pending-popup-content"> 
        <!-- Popup content will be loaded dynamically via AJAX -->
    </div>
    <div id="popup-currency-symbol" style="display:none;"> 
        <!-- Popup content will be loaded dynamically via AJAX -->
    </div>
</div> 

<!-- Update pin success popup -->
<div id="update-pin-popup" style="display: none;">
    <div class="update-pin-popup-content">
        <div id="popup-success-message"></div>
        <div id="note-message"></div>
    </div>
</div>
<!-- Error popup -->
<div id="error-popup" style="display:none;">
    <div class="popup-content">
        
    </div>
</div>

<script type="text/javascript">
    require([
        'jquery',
        'Magento_Ui/js/modal/modal'
    ], function ($, modal) {
        $(document).ready(function () {
            // Initialize modal for less-than-one-balance popup
            var options = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                modalClass: 'less-than-one-balance-popup',
                buttons: []
            };
            var popup = modal(options, $('#less-than-one-balance-popup'));

            var pinoptions = {
                type: 'popup',
                responsive: true,
                title: 'LIX Pay PIN updated successfully.',
                innerScroll: true,
                modalClass: 'update-pin-popup',
                buttons: []
            };
            var pinpopup = modal(pinoptions, $('#update-pin-popup'));

            // Open the popup when clicking on accounts with balance less than 1
            $('.less-than-one-balance').on('click', function () {
                $('#less-than-one-balance-popup').modal('openModal');
                // Perform additional actions if needed
            });
        });
    });

    require([
        'jquery',
        'Magento_Ui/js/modal/modal'
    ], function ($, modal) {
        $(document).ready(function () {
            $('#updatepin-form').on('submit', function (e) {
                $('#error-message').html('');
                $('#success-message').html('');
                $('#note-message').html('');
                e.preventDefault();
                var form = $(this);
                var pin = $('#pin').val();
                var reenterPin = $('#reenter_pin').val();
                // Check if the entered PIN and re-entered PIN are the same
                if (pin !== reenterPin) {
                    document.getElementById("error-message").innerHTML = "Entered PIN and Re-entered PIN does not match.";
                    return;
                }
                if (!/^\d{4}$/.test(pin)) {
                    document.getElementById("error-message").innerHTML = "Your PIN should be a 4 digit number.";
                    return;
                }
                $.ajax({
                    type: form.attr('method'),
                    url: form.attr('action'),
                    data: form.serialize(),
                    dataType: 'json',
                    beforeSend: function () {
                        $('#loader').show();
                    },
                    success: function (response) {
                        if (response.status === 'success') {
                            document.querySelector('.otp-field').style.display = 'block';
                            document.getElementById("success-message").innerHTML = "We had send the OTP on your registered email.";
                            form.off('submit').on('submit', function (e) {
                                e.preventDefault();
                                var pin = $('#pin').val();
                                var otp = $('#otp').val();
                                if (otp === '') {
                                    $('#error-message').html('Please enter OTP.'); // Display error message
                                    return;
                                }
                                if (otp.length < 6) {
                                    $('#error-message').html('Invalid OTP.'); // Display error message
                                    return;
                                }
                                $.ajax({
                                    type: form.attr('method'),
                                    url: form.attr('action'),
                                    data: {
                                        otp: otp,
                                        pin: pin
                                    },
                                    dataType: 'json',
                                    beforeSend: function () {
                                        $('#error-message').html('');
                                        $('#success-message').html('');
                                        $('#loader').show();
                                    },
                                    success: function (response) {
                                        if (response.status === 'success') {
                                            $('#error-message').html('');
                                            $('#success-message').html('');
                                            document.getElementById("note-message").innerHTML = "Please remember your PIN and do not share it with anyone. It will be used to authorize transactions securely.";
                                            $('#update-pin-popup').modal('openModal');
                                            location.reload();
                                            //$('#updatepin-form')[0].reset();
                                        }
                                        else {

                                            document.getElementById("error-message").innerHTML = "Invalid OTP.";
                                        }

                                    },
                                    error: function (xhr, status, error) {
                                        console.error("XHR status:", status);
                                        console.error("Error:", error);
                                        console.error("Response:", xhr.responseText);
                                    },
                                    complete: function () {
                                        $('#loader').hide();
                                    }
                                });
                            });
                        } else {
                            document.getElementById("error-message").innerHTML = "Unable to send OTP.";
                        }

                    },
                    error: function (xhr, status, error) {
                        console.error("XHR status:", status);
                        console.error("Error:", error);
                        console.error("Response:", xhr.responseText);
                    },
                    complete: function () {
                        $('#loader').hide();
                    }
                });
            });
        });
        
        $(document).ready(function(){
            var popupShown = false; 

            var options = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                title: 'Approve Transfer Request',
                buttons: [{
                    text: $.mage.__('Approve'),
                    class: '',
                    click: function () { 
                        approveAction();
                    }
                },{
                    text: $.mage.__('Reject'),
                    class: 'reject-button',
                    click: function () {
                            rejectAction();
                    }
                }]
            };

            var popup = modal(options, $('#popup-modal'));

            var pendingoptions = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                title: 'Pending Transaction',
                 buttons: [{
                    text: $.mage.__('Cancel Transaction'),
                    class: 'cancel-button',
                    click: function () { 
                        var currencySymbol = $('#popup-currency-symbol').text();
                        rejectPendingAction(currencySymbol);
                    }
                },{
                    text: $.mage.__('Close'),
                    class: 'close-button',
                    click: function () {
                        closeAction();
                    }
                }]
            };
            var pendingpopup = modal(pendingoptions, $('#pending-popup-modal'));

            $(document).on('click', '.pending', function (e) {
                e.preventDefault();
                var currencySymbol = $(this).data('currency-symbol');
                if ($(this).hasClass('less-than-one-balance')) {
                    $('#less-than-one-balance-popup').modal('openModal');
                    return false;
                } else {
                    $('#pending-popup-modal').modal('openModal');
                    $('#popup-currency-symbol').html(currencySymbol);
                    return false;
                }

            });

            var insufficientoptions = {
                type: 'popup',
                responsive: true,
                innerScroll: true,
                title: 'Insufficient Balance Transaction',
                 buttons: [{
                    text: $.mage.__('OK'),
                    class: 'ok-tran-button',
                    click: function () { 
                        rejectAction();
                    }
                }]
            };
            var insufficentpopup = modal(insufficientoptions, $('#error-popup'));




            
             //  display popup
            function fetchDataAndDisplayPopup() {
                $.ajax({
                    url: '<?php echo $block->getPopupActionUrl('lixreward/index/apidata'); ?>',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response){
                        if(response.status && !popupShown && response.transactionStatus === 'pending'){ // Check if response status is true and popup hasn't been shown
                            
                            $('#popup-content').html(response.message);
                            $('#popup-conRate').html(response.popupCon);
                            $('#popup-modal').modal('openModal');

                            popupShown = true;
                            $('a.wallets').addClass('pending');
                            $('#pending-popup-content').html(response.pendingMsg);
                        }else if (response.status === 'error') {
                            $('#error-message-content').text(response.message); // Set error message content
                            $('#error-popup').modal('openModal'); // Open the error popup
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    }
                });
            }               
            fetchDataAndDisplayPopup();           
            setInterval(fetchDataAndDisplayPopup, 60000);
                $('#popup-modal').on('modalclosed', function(){
                    if (!popupShown) {
                        return; 
                    }
                    if (confirm('Are you sure you want to close?')) {
                        rejectAction();
                    } else {
                        popupShown = false;
                        fetchDataAndDisplayPopup();
                    }
                });

               

           
            // approve action
                function approveAction() {
                    $('#loader').show();
                    $.ajax({
                        url: '<?php echo $block->getApproveActionUrl('lixreward/lixpay/approve'); ?>', 
                        type: 'POST',
                        dataType: 'json',
                        success: function(response){
                            if (response.status === 'success') {
                                $('#popup-modal').modal('closeModal', $('#popup-modal')); 
                                updateBalance(response.newBalance);
                                setTimeout(function(){
                                    alert(response.responseData);
                                }, 500);
                                popupShown = false;
                            } else {
                                alert('Failed to approve transaction.');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(xhr.responseText);
                        },
                        complete: function() {
                            // Hide loader after the AJAX request is complete
                            $('#loader').hide();
                        }
                    });
                }

                // Add this function to update the balance
                function updateBalance(newBalance) {
                    // Assuming you have an element to display the balance with id 'balance'
                    $('#balance').text(newBalance); // Update balance element with new value
                }

                function closeAction() {
                    $('#pending-popup-modal').modal('closeModal');
                    
                }
            // reject pending action
                function rejectPendingAction(currencySymbol) {
                    var balancePageUrl;
                    $.ajax({
                        url: '<?php echo $block->getRejectActionUrl('lixreward/lixpay/cancel'); ?>', 
                        type: 'POST',
                        dataType: 'json',
                        success: function(response){
                            if (response.status === 'success') {
                                $('#popup-modal').modal('closeModal', $('#popup-modal')); // Close the popup
                                $('#pending-popup-modal').modal('closeModal', $('#pending-popup-modal'));   
                                setTimeout(function(){
                                    alert(response.responseData); // Show the alert message
                                }, 500); // Delay alert by 500 milliseconds to ensure the modal is closed
                                popupShown = false;

                                balancePageUrl = '<?php echo $block->getBaseUrl(); ?>';
                                url = balancePageUrl + 'lixreward/lixpay/balance/wallet/' + currencySymbol;
                                if ($(document).find('.pending').length) {
                                    // If it exists, remove the 'pending' class from those elements
                                    $(document).find('.pending').removeClass('pending');
                                    window.location.href = url;
                                }
                            } else {
                                alert('Failed to approve transaction.');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(xhr.responseText);
                        }
                    });
                }

            // reject action
                function rejectAction() {
                    $.ajax({
                        url: '<?php echo $block->getRejectActionUrl('lixreward/lixpay/cancel'); ?>', 
                        type: 'POST',
                        dataType: 'json',
                        success: function(response){
                            if (response.status === 'success') {
                                $('#popup-modal').modal('closeModal', $('#popup-modal')); // Close the popup
                                $('#pending-popup-modal').modal('closeModal', $('#pending-popup-modal'));
                                $('#error-popup').modal('closeModal', $('#error-popup'));
                                setTimeout(function(){
                                    alert(response.responseData); // Show the alert message
                                }, 500); // Delay alert by 500 milliseconds to ensure the modal is closed
                                popupShown = false;
                                if ($(document).find('.pending').length) {
                                    $(document).find('.pending').removeClass('pending');
                                }
                            } else {
                                alert('Failed to approve transaction.');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error(xhr.responseText);
                        }
                    });
                }
                // approve action
                function pinBalanceAction() {
                    $.ajax({
                        url: '<?php echo $block->getPinStatusUrl('lixreward/lixpay/pinBalance'); ?>', 
                        type: 'POST',
                        dataType: 'json',
                        success: function(response){
                            if (response.status === 'success') {
                                console.log(response);
                            } 
                        },
                        error: function(xhr, status, error) {
                            console.error(xhr.responseText);
                        }
                    });
                }
        });
    });
   
    document.querySelectorAll('.toggle-password').forEach(function (icon) {
        icon.addEventListener('click', function () {
            var inputField = icon.previousElementSibling;
            if (inputField.type === 'password') {
                inputField.type = 'text';
                icon.querySelector('i').classList.remove('fa-eye');
                icon.querySelector('i').classList.add('fa-eye-slash');
            } else {
                inputField.type = 'password';
                icon.querySelector('i').classList.remove('fa-eye-slash');
                icon.querySelector('i').classList.add('fa-eye');
            }
        });
    });

    document.getElementById('otp').addEventListener('input', function (event) {
        let inputValue = event.target.value;
        if (!/^\d*$/.test(inputValue)) {
            document.getElementById("error-message").innerHTML = "OTP must be number";
            event.target.value = inputValue.replace(/[^\d]/g, '');
        }
        if (inputValue.length > 6) {
            event.target.value = inputValue.slice(0, 6);
        }
    });

    // $(document).ready(function () {
    //         $('.disabled-wallet').on('click', function (e) {
    //             e.preventDefault(); // Prevent the default behavior of the link
    //             alert('No balance available in this wallet.'); // Show an alert (you can replace this with a modal popup)
    //             return false;
    //         });
    // });
</script>