<?php
    $fixtures = $block->getTodaysMatchesDatewise();
?>

<div class="match-list-container">
	<div class="matches-list row">
		<?php 
            if (!empty($fixtures)) {
                foreach($fixtures as $date => $matches){ 
                   ?>
                   <div class="match-row">
                       <h2><?php 
                            $dateTime = new \DateTime($date);
                            echo $dateTime->format('l, d F');
                            ?>                           
                       </h2>
                        <?php foreach ($matches as $match){ ?>
                           <div class="matches col-lg-3 col-md-4 col-sm-6" id="match-details">
                                <div class="match-teams">
                                    <div class="home-team col-lg-5 col-md-5 col-sm-5">
                                        <div class="home-team-img">
                                            <img src="<?php echo $match['home_team_logo']; ?>">
                                        </div>
                                        <span><?php echo $match['home_team']; ?></span>
                                    </div>
                                     <div class="match-time col-lg-2 col-md-2 col-sm-2">
                                        <span data-timestamp="<?php echo $match['timestamp']; ?>" class="match-time-span"></span>
                                    </div>
                                     <div class="away-team col-lg-5 col-md-5 col-sm-5">
                                        <div class="away-team-img">
                                            <img src="<?php echo $match['away_team_logo']; ?>">
                                        </div>
                                        <span><?php echo $match['away_team']; ?></span>
                                    </div>
                                    <div class="match-venue">
                	                    <div class="description"><?php echo $match['venue_name'] .",". $match['venue_city']; ?></div>
                                    </div>
                               </div>
                           </div>
                        <?php
                       }?>
                   </div>				
            <?php }
            } else {
                echo "No matches available";
            }
        ?>
	</div>
</div> 

<style type="text/css">
.matches-list.row {
    display: grid;
}
.matches {
    background-color: white;
}
#match-details {
    margin: 10px;
}
</style>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        var timeElements = document.querySelectorAll('.match-time-span');
        timeElements.forEach(function(element) {
            var timestamp = parseInt(element.getAttribute('data-timestamp'), 10);
            var date = new Date(timestamp * 1000);
            var options = {
                timeZone: timezone,
                hour: 'numeric',
                minute: 'numeric',
                hour12: true // Use 12-hour format
            };

            var localDateTime = new Intl.DateTimeFormat([], options).format(date);
            element.textContent = localDateTime;
        });
    });
</script>