<?php
$matchData = $block->getTodaysMatches();
?>
<div class="game-container">
    <h2>Live Matches</h2>
    <div class="live-matches">
        
    </div>
    <h2>Today's Matches</h2>
    <div class="match-list-container">
    	<div class="matches-list row">
    		<?php 
                 if (!empty($matchData)) {
    				//$matchData = $matchList['leagues'];
                    foreach($matchData as $item){ 
                       ?>
                           <div class="matches col-lg-3 col-md-4 col-sm-6" id="match-details" style="background-color: white;">
                                <div class="match-teams">
                                    <div class="home-team col-lg-5 col-md-5 col-sm-5">
                                        <div class="home-team-img">
                                            <img src="<?php echo $item['teams']['home']['logo']; ?>">
                                        </div>
                                        <span><?php echo $item['teams']['home']['name']; ?></span>
                                    </div>
                                     <div class="match-time col-lg-2 col-md-2 col-sm-2">
                                        <span data-timestamp="<?php echo $item['fixture']['timestamp']; ?>" class="match-time-span"></span>
                                         <?php 
                                            //date_default_timezone_set('America/New_York');
                                            $timestamp = $item['fixture']['timezone'];
                                            //echo date('H:i', 1719968400);
                                        ?> 
                                        </span class="vs">V/S</span>
                                    </div>
                                     <div class="away-team col-lg-5 col-md-5 col-sm-5">
                                        <div class="away-team-img">
                                            <img src="<?php echo $item['teams']['away']['logo']; ?>">
                                        </div>
                                        <span><?php echo $item['teams']['away']['name']; ?></span>
                                    </div>
                                    <div class="match-venue">
                	                    <div class="description"><?php echo $item['fixture']['venue']['name'] .",". $item['fixture']['venue']['city']; ?></div>
                                    </div>
                               </div>
                           </div>
                       <?php			
                    }
                } else {
                    echo "No matches available";
                }
            ?>
    	</div>
    </div>
</div>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        var timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        var timeElements = document.querySelectorAll('.match-time-span');
        timeElements.forEach(function(element) {
            var timestamp = parseInt(element.getAttribute('data-timestamp'), 10);
            var date = new Date(timestamp * 1000);
            var options = {
                timeZone: timezone,
                hour: 'numeric',
                minute: 'numeric',
                hour12: true // Use 12-hour format
            };

            var localDateTime = new Intl.DateTimeFormat([], options).format(date);
            element.textContent = localDateTime;
        });
    });
</script>