<?php 
$offerblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Offers');
$recommofferslist = $block->getRecommOffers();
$_helper = $this->helper(\Comave\LixApiConnector\Helper\Data::class);
$blockName = $block->getLayout()->createBlock('Comave\TravellerInfo\Block\GetCustomLink');
$custEmail = $blockName->getCustomerEmail();
$custWallet = $blockName->getLixWallet();
$lixWallet = 'lix_wallet';

if($custWallet == 'LIXX'){
  $currconv = number_format((float)$_helper->getLixCurrenciesValues($custEmail,$lixWallet), 4, '.', '');
}else{
  $currconv = number_format((float)$_helper->getLixCurrenciesValues($custEmail,$lixWallet), 2, '.', '');
}
if ($blockName->getRewardPoint() >= 1) {
  $reward = number_format((float)$blockName->getRewardPoint(), 2, '.', '');
  $rreward = ceil($reward);
}else{
  $reward = number_format((float)$blockName->getRewardPoint(), 3, '.', '');
  $rreward = ceil($reward);
}

if ($blockName->getPoints() >= 1) {
  $currencyBalance = number_format((float)$blockName->getPoints(), 2, '.', '');
}else{
  $currencyBalance = number_format((float)$blockName->getPoints(), 3, '.', '');
}
$couponblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Coupon');
$couponlist = $couponblock->getCouponlistDetails();

$walletblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\WalletData');
$walletBalance = $walletblock->getWalletBalance();

$charityblock = $block->getLayout()->createBlock('Comave\LixApiConnector\Block\Charity');
$details = $charityblock->getWalletListDetails();
$charity = $charityblock->getCharitylistDetails();
if(isset($charity) && $charity['success'] == 1){ 
    $charityData = $charity['data'];   
}
if(isset($details) && $details['success'] == 1){ 
    $detailsData = $details['data'];   
}
$count = 0;
if (isset($couponlist['success']) && $couponlist['success'] == 1) {
    $couponcountData = $couponlist['data']['data'];
    foreach($couponcountData as $itemcount){ 
        $count++;
    }
}
?>
<div class="page-main">
    <h4 class="page-title"><?= $block->escapeHtml(__('my account')) ?></h4>
    <div class="main-section-top">
        <div class="section-main-1">
            <div class="main-inner">
                <h4 class="lix-heading">LIX Rewards</h4>
                <span class="lix-tocken"><?= $block->escapeHtml($walletBalance['points']) ?></span>
                <span class="lix-currency">$<?= $block->escapeHtml($walletBalance['currency_amount']) ?></span>
                <span class="lix-conversion">
                    <?php if (isset($custWallet)) { ?>
                            <p><?= $block->escapeHtml(__('1 LIX Rewards = $')) ?><?= $block->escapeHtml($currconv) ?></p>
                    <?php } ?> 
                </span>
            </div>
            <div>
                <img src="<?php echo $block->getMediaUrl().'wysiwyg/lix-dashboard-img/lix-img-logo.png'; ?>" alt="lix-reward">
            </div>
        </div>
        <div class="section-main-2">
            <div class="section-two-inner">
                <div>
                    <h4><?= $block->escapeHtml(__('Earn Rewards')) ?></h4>
                    <span><?= $block->escapeHtml(__('Unlock rewards effortlessly! Simply complete listed tasks and scan to earn points. Start earning today!')) ?></span>
                </div>
                <div><img src="<?php echo $block->getMediaUrl().'wysiwyg/lix-dashboard-img/earn-logo.png'; ?>" alt="earn-reward"></div>
            </div>
            <div class="section-buttons">
                <a href="<?= $this->getUrl('lixreward/EarnReward/earn/') ?>" class="earn-reward">
                    <button class="earn-primary"><?= $block->escapeHtml(__('Earn Rewards')) ?></button>
                </a>
                <a href="#" class="scan-earn">
                    <button class="scan-secondary"><?= $block->escapeHtml(__('Scan and Earn')) ?></button>
                </a>
            </div>
        </div>
        <div class="section-main-3">
            <div class="section-two-inner">
                <div>
                    <h4><?= $block->escapeHtml(__('Redeem Rewards')) ?></h4>
                    <span><?= $block->escapeHtml(__('Redeem your rewards with exclusive offers. Treat yourself with just a click!')) ?></span>
                </div>
                <div class="lix-card"><img src="<?php echo $block->getMediaUrl().'wysiwyg/lix-dashboard-img/redeem-card.png'; ?>" alt="lix-card"></div>
            </div>
            <div class="redeem-buttons">
                <a href="<?= $this->getUrl('lixreward/lixpay/wallet/') ?>" class="lixpay">                    
                    <button class="earn-primary"><img src="<?php echo $block->getMediaUrl().'wysiwyg/lix-dashboard-img/lix-logo.png'; ?>" alt="lixpay"><?= $block->escapeHtml(__('Card')) ?></button>
                </a>
                <a href="<?= $this->getUrl('lixreward/marketplace/offers/') ?>" class="marketplace">
                    <button class="scan-secondary"><?= $block->escapeHtml(__('Offers')) ?></button>
                </a>
            </div>
        </div>
        <div class="section-main-4">
            <h4 class="section-heading"><?= $block->escapeHtml(__('my wallets')) ?></h4>
            <div class="section-4-lix">
                <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/lix-dashboard-img/lix-brand-logo.png'; ?>" alt="wallet">
                <div>
                    <h4 class="wallet-heading">
                        <?php if ($walletBalance['currency'] !== null) { ?>
                            <?= $block->escapeHtml($walletBalance['currency']) ?>
                            
                    </h4>
                    <h4><?= $block->escapeHtml($walletBalance['points']) ?>  
                    <!-- <//?= $block->escapeHtml($walletBalance['currency_amount']); ?></h4> -->

                    <?php } ?>
                </div>
            </div>
            <!-- <a href="#" class="wallet"> 
                <button class="see-all-btn"><?= $block->escapeHtml(__('see all')) ?></button>
            </a> -->
        </div>

    </div>
</div>
<section class="container-fluid">
    <div class="row">
        <div class="container tabs">
            <ul class="tab-list col-md-12">
                <li id="select-1">
                    <h3><?= $block->escapeHtml(__('Transactions')) ?></h3>
                </li>
                <li id="select-2">
                    <h3><?= $block->escapeHtml(__('LIX Wallets')) ?></h3>
                </li>
                <li id="select-3" class="active">
                    <h3><?= $block->escapeHtml(__('Coupons')) ?></h3>
                    <span class="coupon-count"><?= $block->escapeHtml($count) ?></span>
                </li>
                <li id="select-4" class="analytics-tab">
                    <h3><?= $block->escapeHtml(__('Analytics')) ?></h3>
                </li>                
                <li id="select-5" class="charity-tab">
                    <h3><?= $block->escapeHtml(__('Charity')) ?></h3>
                </li>
            </ul>
        </div>
        <div class="container tab-content-one">
            <div class="tabs-content transaction " id="content-select-1">
                <div class="Lix-main">
                    <ul class="lix-history">
                        <li id="lix-history-cat-1">
                            <h3><?= $block->escapeHtml(__('Earned')) ?></h3>
                        </li>
                        <li id="lix-history-cat-2">
                            <h3><?= $block->escapeHtml(__('Redeemed')) ?></h3>
                        </li>
                        <li id="lix-history-cat-3" class="active">
                            <h3><?= $block->escapeHtml(__('All')) ?></h3>
                        </li>
                    </ul>
                </div>
                <div class="lix-history-content-main">
                    <div class="lix-history-top">
                        <?php if ($earnedRewardHistory = $block->getEarnedRewardHistory()):  ?>
                            <a href="<?= $this->getUrl('reward/customer/info') ?>" class="lix-history-btn"> 
                                <button class="lix-history-see-all-btn"><?= $block->escapeHtml(__('see all')) ?></button>
                            </a>
                        <?php endif;?>
                    </div>
                    <div class="lix-history-content earned" id="content-lix-history-cat-1">
                        <!-- Earned transaction History -->
                        <div class="earned-block-content">
                            <?php if ($earnedRewardHistory = $block->getEarnedRewardHistory()):  ?>
                                <div class="table-wrapper earned-reward-history">
                                    <table class="data table table-earned-reward-history">
                                        <thead>
                                            <tr>
                                                <th scope="col" class="col points"><?= $block->escapeHtml(__('Points')) ?></th>
                                                <th scope="col" class="col balance"><?= $block->escapeHtml(__('Balance')) ?></th>
                                                <th scope="col" class="col amount"><?= $block->escapeHtml(__('Amount')) ?></th>
                                                <th scope="col" class="col date"><?= $block->escapeHtml(__('Date')) ?></th>
                                                <th scope="col" class="col earned"><?= $block->escapeHtml(__('Earned')) ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($earnedRewardHistory as $earnitem): ?>
                                            <tr>
                                                <td data-th="<?= $block->escapeHtml(__('Points')) ?>" class="col points">
                                                    <span>
                                                        <?= $block->escapeHtml($earnitem['points_delta']) ?>                              
                                                    </span>
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('Balance')) ?>" class="col balance">
                                                    <span>
                                                        <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/lix-brand.png'; ?>" alt="balance">
                                                        <?= $block->escapeHtml($earnitem['points_balance']) ?>
                                                    </span>
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('Amount')) ?>" class="col amount">
                                                    <span>
                                                        <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/doller.png'; ?>" alt="amount">
                                                        <?= $block->escapeHtml("$".number_format((float)$earnitem['currency_amount'], 2, '.', '')) ?>
                                                    </span>
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('Date')) ?>" class="col date">
                                                    <?php 
                                                        $date_str = $this->formatDate($earnitem['created_at'], \IntlDateFormatter::SHORT, true);
                                                        $date = DateTime::createFromFormat('n/j/y, g:i A', $date_str);
                                                        $formatted_date = $date->format("F j, Y");
                                                    ?>
                                                    <span class=""><?= $block->escapeHtml($formatted_date) ?></span>                        
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('earned')) ?>" class="col earned">
                                                    <span>
                                                        <?= $block->escapeHtml($earnitem['comment']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="message info empty">
                                    <span>
                                        <?= $block->escapeHtml(__('There is no balance history.')) ?>
                                    </span>
                                </div>
                            <?php endif;?>
                        </div>
                    </div>
                    <div class="lix-history-content reedem" id="content-lix-history-cat-2">
                        <!-- Redeemed transaction History -->
                        <div class="redeemd-block-content">
                            <?php if ($redeemRewardHistory = $block->getRedeemRewardHistory()):  ?>
                                <div class="table-wrapper redeemd-reward-history">
                                    <table class="data table table-redeemd-reward-history">
                                        <thead>
                                            <tr>
                                                <th scope="col" class="col points"><?= $block->escapeHtml(__('Points')) ?></th>
                                                <th scope="col" class="col balance"><?= $block->escapeHtml(__('Balance')) ?></th>
                                                <th scope="col" class="col amount"><?= $block->escapeHtml(__('Amount')) ?></th>
                                                <th scope="col" class="col date"><?= $block->escapeHtml(__('Date')) ?></th>
                                                <th scope="col" class="col redeem"><?= $block->escapeHtml(__('Redeem')) ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($redeemRewardHistory as $redeemitem): ?>
                                            <tr>
                                                <td data-th="<?= $block->escapeHtml(__('Points')) ?>" class="col points">
                                                    <span>
                                                        <?= $block->escapeHtml($redeemitem['points_delta']) ?>                                
                                                    </span>
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('Balance')) ?>" class="col balance">
                                                    <span>
                                                        <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/lix-brand.png'; ?>" alt="balance">
                                                        <?= $block->escapeHtml($redeemitem['points_balance']) ?>
                                                    </span>
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('Amount')) ?>" class="col amount">
                                                    <span>
                                                        <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/doller.png'; ?>" alt="amount">
                                                        <?= $block->escapeHtml("$".number_format((float)$redeemitem['currency_amount'], 2, '.', '')) ?>
                                                    </span>
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('Date')) ?>" class="col date">
                                                    <?php 
                                                        $date_str = $this->formatDate($redeemitem['created_at'], \IntlDateFormatter::SHORT, true);
                                                        $date = DateTime::createFromFormat('n/j/y, g:i A', $date_str);
                                                        $formatted_date = $date->format("F j, Y");
                                                    ?>
                                                    <span class=""><?= $block->escapeHtml($formatted_date) ?></span>                        
                                                </td>
                                                <td data-th="<?= $block->escapeHtml(__('redeem')) ?>" class="col redeem">
                                                    <span>
                                                        <?= $block->escapeHtml($redeemitem['reedem_comment']) ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="message info empty">
                                    <span>
                                        <?= $block->escapeHtml(__('There is no balance history.')) ?>
                                    </span>
                                </div>
                            <?php endif;?>
                        </div>
                    </div>
                    <div class="lix-history-content all active" id="content-lix-history-cat-3">
                        <!-- All transaction History -->
                        <div class="all-block-content">
                            <?php if ($allRewardHistory = $block->getallRewardHistory()):  ?>
                            <div class="table-wrapper all-reward-history">
                                <table class="data table table-all-reward-history">
                                    <thead>
                                        <tr>
                                            <th scope="col" class="col points"><?= $block->escapeHtml(__('Points')) ?></th>
                                            <th scope="col" class="col balance"><?= $block->escapeHtml(__('Balance')) ?></th>
                                            <th scope="col" class="col amount"><?= $block->escapeHtml(__('Amount')) ?></th>
                                            <th scope="col" class="col date"><?= $block->escapeHtml(__('Date')) ?></th>
                                            <th scope="col" class="col earned"><?= $block->escapeHtml(__('Earned')) ?></th>
                                            <th scope="col" class="col redeem"><?= $block->escapeHtml(__('Redeem')) ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($allRewardHistory as $item): ?>
                                        <tr>
                                            <td data-th="<?= $block->escapeHtml(__('Points')) ?>" class="col points">
                                                <span>
                                                    <?= $block->escapeHtml($item['points_delta']) ?>                                
                                                </span>
                                            </td>
                                            <td data-th="<?= $block->escapeHtml(__('Balance')) ?>" class="col balance">
                                                <span>
                                                    <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/lix-brand.png'; ?>" alt="balance">
                                                    <?= $block->escapeHtml($item['points_balance']) ?>
                                                </span>
                                            </td>
                                            <td data-th="<?= $block->escapeHtml(__('Amount')) ?>" class="col amount">
                                                <span>
                                                    <img src="<?php echo $blockName->getMediaUrl() . 'wysiwyg/pearl_theme/doller.png'; ?>" alt="amount">
                                                    <?= $block->escapeHtml("$".number_format((float)$item['currency_amount'], 2, '.', '')) ?>
                                                </span>
                                            </td>
                                            <td data-th="<?= $block->escapeHtml(__('Date')) ?>" class="col date">
                                                <?php 
                                                    $date_str = $this->formatDate($item['created_at'], \IntlDateFormatter::SHORT, true);
                                                    $date = DateTime::createFromFormat('n/j/y, g:i A', $date_str);
                                                    $formatted_date = $date->format("F j, Y");
                                                ?>
                                                <span class=""><?= $block->escapeHtml($formatted_date) ?></span>                        
                                            </td>
                                            <td data-th="<?= $block->escapeHtml(__('earned')) ?>" class="col earned">
                                                <span>
                                                    <?= $block->escapeHtml($item['comment']) ?>
                                                </span>
                                            </td>
                                            <td data-th="<?= $block->escapeHtml(__('redeem')) ?>" class="col redeem">
                                                <span>
                                                    <?= $block->escapeHtml($item['reedem_comment']) ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="message info empty">
                                <span>
                                    <?= $block->escapeHtml(__('There is no balance history.')) ?>
                                </span>
                            </div>
                            <?php endif;?>
                        </div>
                    </div>
                </div>

            </div>
            <div class="tabs-content lix-wallet " id="content-select-2">
                <div class="tab-header col-md-12 text-center">
                    <div class="wallet-content">
                        <div class="my-wallets">
                            <h4 class="wallet-heading">
                            <?php if ($walletBalance['currency'] !== null) { ?>
                            <?= $block->escapeHtml($walletBalance['currency'].":") ?>
                            <?= $block->escapeHtml($walletBalance['points']) ?> - $<?= $block->escapeHtml($walletBalance['currency_amount']); ?>

                            <?php } ?>
                            </h4>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tabs-content coupon active" id="content-select-3">
                <div class="tab-header col-md-12 text-center">                    
                    <div class="coupons-top">
                         <?php 
                            if (isset($couponlist['success']) && $couponlist['success'] == 1) {
                                ?>
                                <a href="<?= $this->getUrl('lixreward/coupon/mycoupon/') ?>" class="coupons-btn"> 
                                    <button class="coupons-see-all-btn"><?= $block->escapeHtml(__('see all')) ?></button>
                                </a>
                            <?php
                            }
                            ?>
                    </div>
                    <div class="coupon-list-container">
                        <div class="coupons-list row">
                            <?php 
                                if (isset($couponlist['success']) && $couponlist['success'] == 1) {
                                    $couponData = $couponlist['data']['data'];
                                    foreach($couponData as $item){ 
                                       ?>
                                       <div class="coupons col-lg-3 col-md-4 col-sm-6 mb-3" id="coupon-details">
                                            <div class="coupon-grid">
                                                <div class="offer-img-container">
                                                    <img src="<?php echo $item['market']['offer_image']; ?>">
                                                </div>
                                                <div class="description"><?php echo $item['description']; ?></div>
                                            </div>
                                       </div>

                                       <?php
                                       $couponDetails = $couponblock->getCouponDetails($item['id']);
                                       ?>
                                       <!-- Coupon container -->
                                        <?php 
                                            if (isset($couponDetails['success']) && $couponDetails['success'] == 1 && isset($couponDetails['data'])) {
                                            ?>
                                            <div class="coupon-details-popup-container">
                                                <div class="coupon-details-container" id="coupon-details-popup">
                                                    <span class="coupon-details-popup-close">&times;</span>
                                                    <div class="coupon-body row">
                                                        <div class="coupon-desc"><?php echo $couponDetails['data']['description']; ?></div>
                                                        <div class="coupon-url">
                                                            <div class="coupon-url-link">
                                                                <a id="couponLink" href="<?php echo $couponDetails['data']['coupon']; ?>" target="_blank">
                                                                    <?php echo $couponDetails['data']['coupon']; ?>                                        
                                                                </a>
                                                            </div>
                                                            <div class="copy-button">                                        
                                                                <button class="copy-coupon-url" onclick="copyCouponLink('<?php echo $couponDetails['data']['coupon']; ?>')"><i class="icon-line-stack-2"></i></button>
                                                            </div>
                                                        </div>
                                                        <div class="show-qrcode">
                                                            <button class="coupon-qrcode"><?= $block->escapeHtml(__('Show QR Code')) ?></button>
                                                            <button class="hide-coupon-qrcode" style="display: none;"><?= $block->escapeHtml(__('Hide QR Code')) ?></button>
                                                            <div>
                                                                <?php 
                                                                $qr="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=".$couponDetails['data']['coupon'];
                                                                ?>
                                                                <img class="coupon-qrcode-img" src="<?php echo $qr; ?>" alt="QR Code" style="display: none;">
                                                            </div>
                                                        </div>
                                                        <div class="show-barcode">
                                                            <button class="coupon-barcode"><?= $block->escapeHtml(__('Show Barcode')) ?></button>
                                                            <button class="hide-coupon-barcode" style="display: none;"><?= $block->escapeHtml(__('Hide Barcode')) ?></button>
                                                            <div>
                                                                <img class="coupon-barcode-img" src="data:image/png;base64,<?php echo $couponDetails['data']['coupon_barcode']; ?>" alt="Barcode" style="display: none;">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php
                                        }               
                                    }
                                } else {
                                    ?>
                                    <div class="message info empty">
                                        <span>
                                            <?= $block->escapeHtml(__('No coupons available.')) ?>
                                        </span>
                                    </div>
                                    <?php
                                }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tabs-content analytics" id="content-select-4">
                <div class="tab-header col-md-12 text-center">
                    <!-- <h3>Analytics</h3> -->
                </div>
            </div>
            <div class="tabs-content charity" id="content-select-5">
                <div class="tab-header col-md-12 text-center">
                    <div class="charity-details">    
                        <div class="user-wallets-list">
                            <label><?= $block->escapeHtml(__('Select wallet')) ?></label>
                            <div class="wallets-list-wrapper">
                                <select name="wallet_id" id="wallet_id">
                                    <option value="">
                                        <?= $block->escapeHtml(__('Select wallet')) ?>
                                    </option>
                                    <?php 
                                    $walletSymbol = $block->getLixxWalletSymbol();
                                    if (isset($detailsData)) {
                                        foreach($detailsData as $detail){ 
                                            if($walletSymbol == $detail['currency_symbol']){
                                                $balance = number_format((float)$detail['max_balance'], 3, '.', '');
                                            }
                                            else{
                                                $balance = $detail['balance'];
                                            }
                                           ?>
                                            <option value="<?php echo $detail['custom_currency_id']; ?>" data-balance="<?php echo $balance; ?>">
                                                <?php echo $detail['currency_symbol']. " - ".$balance; ?>
                                            </option>
                                            <?php
                                        }
                                    } else {
                                        ?>
                                            <option value="">
                                                <?php echo $block->escapeHtml(__('No wallets are assigned')); ?>
                                            </option>
                                    <?php
                                    }
                                ?>
                                </select>
                            </div>
                        </div> 
                        <div id="wallet-error-message"></div>
                        <div id="balance-display"></div> 
                        <div class="charity-list">
                            <label><?= $block->escapeHtml(__('Select charity foundation')) ?></label>
                            <div class="charity-list-wrapper">
                                <select name="charity_id" id="charity_id">
                                    <option value="">
                                        <?= $block->escapeHtml(__('Select charity foundation')) ?>
                                    </option>
                                    <?php 
                                    if (isset($charityData)) {
                                        foreach($charityData as $charities){ 
                                           ?>
                                            <option value="<?php echo $charities['id']; ?>">
                                                <?= $escaper->escapeHtml($charities['name']); ?>
                                            </option>
                                            <?php
                                        }
                                    } else {
                                        ?>
                                            <option value="">
                                                <?php echo $block->escapeHtml(__('No charity foundation available')); ?>
                                            </option>
                                    <?php
                                    }
                                ?>
                                </select>
                            </div>
                        </div>
                        <div id="charity-error-message"></div>       
                        <div class="charity-amount">
                            <label><?= $block->escapeHtml(__('Amount')) ?></label>
                            <div class="charity-amount-wrapper">
                                <input type="text" id="amount" name="amount">
                            </div>
                        </div>
                        <div id="amount-error-message"></div> 
                        <div id="rem-balance-display"></div> 
                        <div id="charity-errormsg"></div>      
                        <!-- Loader element  -->
                        <div id="charity-loader" style="display: none;">
                            <img src="<?php echo $block->getMediaUrl() . 'wysiwyg/pearl_theme/pin_loader.gif'; ?>"
                                alt="Loading...">
                        </div>
                        <div class="submit-charity-form">
                            <button id="donateButton"><?= $block->escapeHtml(__('Donate')) ?></button>
                        </div>    
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="recommended-offers">
    <div class="recommended-offers-top">
        <h4><?= $block->escapeHtml(__('Recommended')) ?></h4>
        <a href="<?= $this->getUrl('lixreward/marketplace/offers/') ?>" class="offers-btn"> 
            <button class="offers-see-all-btn"><?= $block->escapeHtml(__('see all')) ?></button>
        </a>
    </div>
    <div class="offers wrapper grid offers-grid">
        <div class="offers grid items offers-items slick-carousel">
            <?php 
            if (isset($recommofferslist['success']) && $recommofferslist['success'] == 1) {
                $recommoffers = $recommofferslist['data'];
                foreach($recommoffers as $recommoffer){?>
                    <a href="<?php echo $offerblock->getofferDetailPageUrl($recommoffer['id']); ?>" class="recomm-redeem-link">
                        <div class="item offer offer-item">
                            <div class="offer-item-info item" data-container="offer-grid">
                                <div class="offer-img-container">
                                    <img src="<?php echo $recommoffer['offer_image']; ?>" alt="offer">
                                </div>
                                <div class="brand-name">
                                    <?= $block->escapeHtml($recommoffer['brand_name']) ?>
                                </div>
                                <div class="benefit">
                                    <?= $block->escapeHtml($recommoffer['benefit']) ?>
                                </div>
                                <div class="recommcurrency-wallet">
                                    <div class="recommoffer-currency">
                                        <div class="recommoffer-currency-img"></div>
                                        <span>
                                            <?= $escaper->escapeHtml($recommoffer['offer_currency']); ?>
                                        </span>
                                    </div>
                                    <div class="recomm-currency">
                                        <div class="recommcurrency-img"></div>
                                        <span>
                                            <?= $escaper->escapeHtml($recommoffer['currency']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
            <?php 
                } 
            }
        ?>
        </div>
    </div>
</div>
<!-- Popup container -->
<div class="charity-popup-success">
    <div class="charity-popup-container" id="charity-data-popup">
        <span class="charity-popup-close">&times;</span>
        <div class="charity-body row">
            <div id="charity-successmsg"></div>
        </div>
    </div>
</div>
<script>
    function copyCouponLink(couponUrl) {
        var tempInput = document.createElement("input");
        tempInput.value = couponUrl;
        document.body.appendChild(tempInput);
        tempInput.select();
        document.execCommand("copy");
        document.body.removeChild(tempInput);
    }
    document.addEventListener("DOMContentLoaded", function() {
        var walletSelect = document.getElementById("wallet_id");
        var amount = document.getElementById("amount");
        var balanceDisplay = document.getElementById("balance-display");
        var rembalanceDisplay = document.getElementById("rem-balance-display");
        var walleterrorMessage = document.getElementById('wallet-error-message');
        var charityerrorMessage = document.getElementById('charity-error-message');
        var amounterrorMessage = document.getElementById('amount-error-message');
        var donateButton = document.getElementById("donateButton");

        // Function to disable the donate button
        function disableDonateButton() {
            donateButton.disabled = true;
        }

        // Function to enable the donate button
        function enableDonateButton() {
            donateButton.disabled = false;
        }
        walletSelect.addEventListener("change", function() {
            var selectedOption = this.options[this.selectedIndex];
            var balance = selectedOption.getAttribute("data-balance");
            if (balance <= 0) {
                balanceDisplay.textContent = '';
                walleterrorMessage.textContent = 'Selected wallet has insufficient balance. Please select another wallet.';
            } else {
                walleterrorMessage.textContent = '';
                charityerrorMessage.textContent = '';
                balanceDisplay.textContent = "Available balance: "+balance;
            }
        });

        amount.addEventListener('input', function (event) {
            var inputValue = event.target.value;
            var selectedWallet = walletSelect.options[walletSelect.selectedIndex];
            var walletBalance = parseFloat(selectedWallet.getAttribute("data-balance"));
            amounterrorMessage.innerHTML = "";
            if (!isNaN(inputValue) && inputValue > 0) {
                var remainingBalance = walletBalance - inputValue;
                if (remainingBalance < 0) {
                    rembalanceDisplay.textContent = "Insufficient funds";
                    event.preventDefault();
                } else {
                    rembalanceDisplay.textContent = "Remaining balance: " + remainingBalance.toFixed(3);
                }
            } else {
                rembalanceDisplay.textContent = "";
                amounterrorMessage.innerHTML += 'Please enter a valid amount greater than 0.';
                event.preventDefault();
            }
        });
    });
    require(['jquery','slick'], function ($) {
        
        $(document).ready(function () {
            let promotionSlider = $('.offers.grid.items.offers-items.slick-carousel');
            if (promotionSlider.length) {

                promotionSlider.slick({
                    slidesToShow: 4,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 5000,
                    arrows: true,
                    dots: false,
                    responsive: [
                        {
                            breakpoint: 1023,
                            settings: {

                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 3,
                            }
                        },
                        {
                            breakpoint: 768,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 2
                            }
                        },
                        {
                            breakpoint: 576,
                            settings: {
                                arrows: true,
                                centerPadding: '50px',
                                slidesToShow: 1
                            }
                        },
                        {
                            breakpoint: 480,
                            settings: {
                                arrows: true,
                                slidesToShow: 1
                            }
                        }
                    ],
                });
            }

        
        });

        $(".tab-list li").on("click", function () {
            var tabId = ".tab-list li#" + $(this).attr("id");
            var tabDivId = ".tabs-content#content-" + $(this).attr("id");

            if (!$(this).hasClass("active")) {
                $(".tab-list li").removeClass("active");
                $(this).addClass("active");

                $(".tabs-content").removeClass("active");
                $(tabDivId).addClass("active");
            }
        });
        $(".lix-history li").on("click", function () {
            var tabId = ".lix-history li#" + $(this).attr("id");
            var tabDivId = ".lix-history-content#content-" + $(this).attr("id");

            if (!$(this).hasClass("active")) {
                $(".lix-history li").removeClass("active");
                $(this).addClass("active");

                $(".lix-history-content").removeClass("active");

                $(tabDivId).addClass("active");
            }
        })
    });
    require(['jquery'], function ($) {
    $(document).ready(function () {
        function validateForm() {
            var walletSelect = document.getElementById('wallet_id').value;
            var amountInput = document.getElementById('amount').value;
            var charitySelect = document.getElementById('charity_id').value;
            var isValid = true;
            if (walletSelect === '') {
                document.getElementById('wallet-error-message').textContent = 'Please select a wallet.';
                isValid = false;
            }

            // Amount validation
            var amount = parseFloat(amountInput);
            if (isNaN(amount) || amount <= 0) {
                document.getElementById('amount-error-message').textContent = ' ';
                document.getElementById('amount-error-message').textContent = 'Please enter Amount.';
                isValid = false;
            }

            // Charity validation
            if (charitySelect === '') {
                document.getElementById('charity-error-message').textContent = 'Please select a charity foundation.';
                isValid = false;
            }
                var rembalanceDisplay = document.getElementById("rem-balance-display");
                var amounterrorMessage = document.getElementById('amount-error-message');
                var walletId = $('#wallet_id').val();
                var selectedOption = $('#wallet_id option[value="' + walletId + '"]');
                var walletBalance = selectedOption.data('balance');
                if (!isNaN(amountInput) && amountInput > 0) {
                    var remainingBalance = walletBalance - amountInput;
                    if (remainingBalance < 0) {
                        rembalanceDisplay.textContent = "Insufficient funds";
                        isValid = false;
                    } 
                } 

            return isValid;
        }

        $('.submit-charity-form button').click(function (e) {
            e.preventDefault();
            var walletSelect = document.getElementById('wallet_id').value;
            var amountInput = document.getElementById('amount').value;
            var charitySelect = document.getElementById('charity_id').value;
            document.getElementById("wallet-error-message").innerHTML = " ";
            document.getElementById("charity-error-message").innerHTML = " ";
            document.getElementById("amount-error-message").innerHTML = " ";
            if (validateForm()) {
                var formData = {
                    wallet_id: walletSelect,
                    amount: amountInput,
                    charity_id: charitySelect
                };
                $.ajax({
                    url: "<?php echo $charityblock->getChatity(); ?>",
                    method: 'POST',
                    data: formData,
                    beforeSend: function () {
                        $('#charity-loader').show();
                    },
                    success: function (response) {
                        if (response.status == "success") {
                            document.getElementById("charity-errormsg").innerHTML = " ";
                            document.getElementById("charity-successmsg").innerHTML = response.responseData.message;
                            var bodyClasses = document.body.className;
                            var newClass = 'charity-popup-preview';
                            document.body.classList.add(newClass);
                            $('#charity-data-popup').show();
                        }
                        else {
                            document.getElementById("charity-errormsg").innerHTML = response.responseData.message;
                            return;
                        }

                    },
                    error: function (xhr, status, error) {
                    },
                    complete: function () {
                        $('#charity-loader').hide();
                    }
                });
            }
        });
        $('.charity-popup-close').click(function (e) {
            var newClass = 'charity-popup-preview';
            if (document.body.classList.contains(newClass)) {
                document.body.classList.remove(newClass);
            }
            $('#charity-data-popup').hide();
            location.reload();

        });

        $('.coupons').each(function(index) {
            var details = $(this);
            details.click(function(e) {
                e.preventDefault();
                var newClass = 'mycoupon-preview';
                document.body.classList.add(newClass);
                details.next('.coupon-details-popup-container').show();
            });
        });
        $('.coupon-details-popup-close').click(function(e) {
            e.preventDefault();
            var newClass = 'mycoupon-preview';
            if (document.body.classList.contains(newClass)) {
                document.body.classList.remove(newClass);
            }
            var container = $(this).closest('.coupon-details-popup-container');
            container.hide();
            // Reset QR Code and Barcode visibility
            container.find('.coupon-qrcode-img').hide();
            container.find('.coupon-barcode-img').hide();
            container.find('.coupon-qrcode').show();
            container.find('.hide-coupon-qrcode').hide();
            container.find('.coupon-barcode').show();
            container.find('.hide-coupon-barcode').hide();
        });

        $(document).on('click', '.coupon-qrcode', function() {
            var qrCodeImg = $(this).closest('.coupon-body').find('.coupon-qrcode-img');
            qrCodeImg.css('display', 'inline-block');
            $(this).siblings('.hide-coupon-qrcode').show();
            $(this).hide();
        });

        $(document).on('click', '.hide-coupon-qrcode', function() {
            var qrCodeImg = $(this).closest('.coupon-body').find('.coupon-qrcode-img');
            qrCodeImg.hide();
            $(this).siblings('.coupon-qrcode').show();
            $(this).hide();
        });

        $(document).on('click', '.coupon-barcode', function() {
            var barcodeImg = $(this).closest('.coupon-body').find('.coupon-barcode-img');
            barcodeImg.css('display', 'inline-block');
            $(this).siblings('.hide-coupon-barcode').show();
            $(this).hide();
        });

        $(document).on('click', '.hide-coupon-barcode', function() {
            var barcodeImg = $(this).closest('.coupon-body').find('.coupon-barcode-img');
            barcodeImg.hide();
            $(this).siblings('.coupon-barcode').show();
            $(this).hide();
        });
    });
});
</script>