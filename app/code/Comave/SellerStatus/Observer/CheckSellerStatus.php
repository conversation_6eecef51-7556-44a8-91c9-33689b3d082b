<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Observer;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Http\Context;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\UrlInterface;
use Magento\LoginAsCustomerApi\Api\GetLoggedAsCustomerAdminIdInterface;
use Webkul\Marketplace\Helper\Data;

class CheckSellerStatus implements ObserverInterface
{
    /**
     * @var array|string[]
     */
    private array $allowedPaths = [
        'onboarding/',
        'mui/',
        'seller_payouts/',
        'closed',
        'rejected',
        'suspended',
        'deactivated',
        'closed',
        'underReview',
        'logout'
    ];

    /**
     * @param GetLoggedAsCustomerAdminIdInterface $getLoggedAsCustomerAdminId
     * @param Session $customerSession
     * @param Data $mpHelper
     * @param UrlInterface $urlBuilder
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerCompanyProvider $sellerCompanyProvider
     */
    public function __construct(
        private readonly GetLoggedAsCustomerAdminIdInterface $getLoggedAsCustomerAdminId,
        private readonly Session $customerSession,
        private readonly Data $mpHelper,
        private readonly UrlInterface $urlBuilder,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(Observer $observer): void
    {
        /** @var RequestInterface $request */
        $request = $observer->getRequest();

        if (
            $this->getLoggedAsCustomerAdminId->execute() !== 0 ||
            !$this->customerSession->isLoggedIn()
            ) {
            return;
        }

        if ($request->isAjax()) {
            return;
        }

        $isSeller = (bool) $this->mpHelper->isSeller();

        if ($isSeller === false) {
            return;
        }

        /** @var \Magento\Framework\HTTP\PhpEnvironment\Response $response */
        $response = $observer->getResponse();

        if ($response->isRedirect()) {
            return;
        }

        foreach ($this->allowedPaths as $path) {
            if (str_contains($request->getRequestUri(), $path)) {
                return;
            }
        }

        $sellerCompany = $this->sellerCompanyProvider->get();
        $userRole = current(
            $this->companyUserRoleManagement->getRolesForCompanyUser(
                (int) ($this->mpHelper->getCustomerId() ?: $this->customerSession->getCustomerId()),
                (int) $sellerCompany->getId()
            )
        );

        if (empty($userRole)) {
            $logoutUrl = $this->urlBuilder->getUrl('customer/account/logout');
            $response->setRedirect($logoutUrl);
            $request->setDispatched();

            return;
        }

        $roleActionInstance = $userRole->getExtensionAttributes()->getStatusInstance();

        if ($roleActionInstance instanceof ActionableInterface) {
            $roleActionInstance->act($response);
        }
    }
}
