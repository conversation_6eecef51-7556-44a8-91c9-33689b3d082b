<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Plugin;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Api\RoleProviderInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\RoleProviderManagementComposite;
use Magento\Company\Api\CompanyManagementInterface;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Model\AbstractModel;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller;

class AssignSellerStatus
{
    /**
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param RequestInterface $request
     * @param CompanyManagementInterface $companyManagement
     * @param RoleProviderManagementComposite $roleProviderManagement
     * @param LoggerInterface $logger
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param ResponseInterface $response
     */
    public function __construct(
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly RequestInterface $request,
        private readonly CompanyManagementInterface $companyManagement,
        private readonly RoleProviderManagementComposite $roleProviderManagement,
        private readonly LoggerInterface $logger,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly ResponseInterface $response,
    ) {
    }

    /**
     * @param Seller $sellerResourceModel
     * @param Seller $result
     * @param AbstractModel $sellerModel
     * @return Seller
     */
    public function afterSave(
        Seller $sellerResourceModel,
        Seller $result,
        AbstractModel $sellerModel
    ): Seller {
        try {
            $sellerId = (int) $sellerModel->getSellerId();
            $sellerCompany = $this->sellerCompanyProvider->get();
            $assignedCompany = $this->companyManagement->getByCustomerId($sellerId);

            if ($sellerCompany->getId() !== $assignedCompany?->getId()) {
                $this->companyManagement->assignCustomer(
                    $sellerCompany->getId(),
                    $sellerId
                );

                $customer = $this->request->getParam('customer');

                if (!empty($customer)) {//bypass for company users since the admin is not assigning a company per say
                    $customer['company_ids'] = [$sellerCompany->getId()];
                    $this->request->setParam('customer', $customer);
                }
            }

            $currentRoles = $this->companyUserRoleManagement->getRolesForCompanyUser(
                $sellerId,
                (int) $sellerCompany->getId()
            );
            /** @var RoleInterface $currentRole */
            $currentRole = current($currentRoles);

            if ($currentRole->getExtensionAttributes()->getIsForcedAssigned() > 0) {
                return $result;
            }

            $roleResult = $this->roleProviderManagement->getForSeller($sellerId);

            if (!$roleResult instanceof RoleProviderInterface) {
                throw new \InvalidArgumentException('Unable to identify role');
            }

            if (empty($roleResult->getRole()) || $currentRole->getRoleName() === $roleResult->getRole()?->getRoleName()) {
                return $result;
            }

            if ($roleResult instanceof ActionableInterface) {
                $roleResult->act($this->response);
            }

            $this->companyUserRoleManagement->setRolesForCompanyUser(
                (int) $sellerId,
                (int) $sellerCompany->getId(),
                [$roleResult->getRole()->getId()]
            );
            $this->logger->info(
                'Successfully assigned seller status',
                [
                    'role' => $roleResult->getRole()->getRoleName()
                ]
            );
        } catch (\Exception $e) {
            $this->logger->warning(
                'Unable to assign seller status',
                [
                    'message' => $e->getMessage(),
                ]
            );
        }

        return $result;
    }
}
