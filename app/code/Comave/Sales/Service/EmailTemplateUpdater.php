<?php

declare(strict_types=1);

namespace Comave\Sales\Service;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Module\Dir;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Psr\Log\LoggerInterface;

/**
 * Service class for updating email templates
 */
class EmailTemplateUpdater
{
    /**
     * Template mappings for ComAve email templates
     */
    private const TEMPLATE_MAPPINGS = [
        'New Order - comave' => 'order_new_comave.html',
        'New Order for Guest - comave' => 'order_new_guest_comave.html',
        'order update - comave' => 'order_update_comave.html',
        'Order Update for Guest - comave' => 'order_update_guest_comave.html',
        'New Invoice- comave' => 'new_invoice_comave.html',
        'New Invoice for Guest - comave' => 'new_invoice_guest_comave.html',
        'New Shipment - comave' => 'new_shipment_comave.html',
        'New Shipment Guest- comave' => 'new_shipment_guest_comave.html',
        'Shipment Update - comave' => 'shipment_update_comave.html',
        'Shipment Update Guest - comave' => 'shipment_update_guest_comave.html'
    ];

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param DirReader $dirReader
     * @param File $fileDriver
     * @param IoFile $ioFile
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly DirReader $dirReader,
        private readonly File $fileDriver,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Update all ComAve email templates
     *
     * @return array
     */
    public function updateAllTemplates(): array
    {
        $successCount = 0;
        $failureCount = 0;
        $results = [];

        foreach (self::TEMPLATE_MAPPINGS as $templateCode => $templateFile) {
            try {
                $this->updateEmailTemplate($templateCode, $templateFile);
                $this->logger->info("Successfully updated email template: {$templateCode}");
                $results[$templateCode] = 'success';
                $successCount++;
            } catch (\Exception $e) {
                $this->logger->error("Failed to update email template {$templateCode}: " . $e->getMessage(), [
                    'template_code' => $templateCode,
                    'template_file' => $templateFile,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                $results[$templateCode] = 'failed: ' . $e->getMessage();
                $failureCount++;
            }
        }

        $this->logger->info("Email template update completed", [
            'total_templates' => count(self::TEMPLATE_MAPPINGS),
            'successful_updates' => $successCount,
            'failed_updates' => $failureCount
        ]);

        return [
            'total' => count(self::TEMPLATE_MAPPINGS),
            'success' => $successCount,
            'failed' => $failureCount,
            'results' => $results
        ];
    }

    /**
     * Update individual email template
     *
     * @param string $templateCode
     * @param string $templateFile
     * @throws \Exception
     */
    private function updateEmailTemplate(string $templateCode, string $templateFile): void
    {
        $moduleEtcDir = $this->dirReader->getModuleDir(Dir::MODULE_ETC_DIR, 'Comave_Sales');
        $templatePath = $moduleEtcDir . DIRECTORY_SEPARATOR . 'install-data' . DIRECTORY_SEPARATOR . $templateFile;
        
        if (!$this->fileDriver->isExists($templatePath)) {
            throw new \Exception("Template file not found: {$templatePath}");
        }

        if (!$this->fileDriver->isReadable($templatePath)) {
            throw new \Exception("Template file is not readable: {$templatePath}");
        }

        $templateContent = $this->ioFile->read($templatePath);
        if ($templateContent === false) {
            throw new \Exception("Could not read template file: {$templatePath}");
        }

        if (empty(trim($templateContent))) {
            throw new \Exception("Template file is empty: {$templatePath}");
        }

        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('email_template');

        $select = $connection->select()
            ->from($tableName, ['template_id', 'template_code'])
            ->where('template_code = ?', $templateCode);
        
        $existingTemplate = $connection->fetchRow($select);
        
        if (!$existingTemplate) {
            $this->logger->warning("Template with code '{$templateCode}' not found in database. Skipping update.");
            return;
        }

        $affectedRows = $connection->update(
            $tableName,
            [
                'template_text' => $templateContent,
                'modified_at' => new \Zend_Db_Expr('NOW()')
            ],
            ['template_code = ?' => $templateCode]
        );

        if ($affectedRows === 0) {
            $this->logger->warning("No rows updated for template: {$templateCode}. Template may already be up to date.");
        } else {
            $this->logger->info("Successfully updated template: {$templateCode} (Template ID: {$existingTemplate['template_id']})");
        }
    }
}
