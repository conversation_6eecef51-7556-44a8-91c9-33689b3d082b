<?php

declare(strict_types=1);

namespace Comave\Sales\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\DeploymentConfig;

/**
 * Observer to add frontend_base_url variable to email templates
 */
class EmailTemplateVarsObserver implements ObserverInterface
{
    /**
     * Configuration key for frontend base URL
     */
    private const FRONTEND_BASE_URL_CONFIG_KEY = 'frontend_base_url';

    /**
     * @param DeploymentConfig $deploymentConfig
     */
    public function __construct(
        private readonly DeploymentConfig $deploymentConfig
    ) {
    }

    /**
     * Add frontend_base_url from env.php to email template variables
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $transport = $observer->getEvent()->getTransport();

        if ($transport) {
            $frontendBaseUrl = $this->deploymentConfig->get(self::FRONTEND_BASE_URL_CONFIG_KEY);

            if ($frontendBaseUrl) {
                $transport->setData(self::FRONTEND_BASE_URL_CONFIG_KEY, rtrim($frontendBaseUrl, '/'));
            }
        }
    }
}
