<?php
/**
 * Copyright © ComAve, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Comave\Sales\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Psr\Log\LoggerInterface;
use Comave\Sales\Service\EmailTemplateUpdater;

/**
 * Update all ComAve email templates to fix frontend URLs (V7)
 */
class UpdateAllEmailTemplates implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param State $appState
     * @param LoggerInterface $logger
     * @param EmailTemplateUpdater $emailTemplateUpdater
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly State $appState,
        private readonly LoggerInterface $logger,
        private readonly EmailTemplateUpdater $emailTemplateUpdater
    ) {
    }

    /**
     * Apply data patch to update all ComAve email templates
     *
     * @return UpdateAllEmailTemplates
     */
    public function apply(): UpdateAllEmailTemplates
    {
        $connection = $this->moduleDataSetup->getConnection();
        $connection->startSetup();

        try {
            $this->appState->setAreaCode(Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            $this->logger->debug('Area code already set or failed to set', [
                'error' => $e->getMessage()
            ]);
        }

        $results = $this->emailTemplateUpdater->updateAllTemplates();

        $this->logger->info('Email template update patch completed', $results);

        $connection->endSetup();

        return $this;
    }

    /**
     * Get patch aliases
     *
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * Get patch dependencies
     *
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }
}
