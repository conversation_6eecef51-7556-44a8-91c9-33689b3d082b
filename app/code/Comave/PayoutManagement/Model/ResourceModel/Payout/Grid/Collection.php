<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\ResourceModel\Payout\Grid;

use Magento\Framework\Api\Search\SearchResultInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Search\AggregationInterface;
use Comave\PayoutManagement\Model\ResourceModel\Payout\Collection as PayoutCollection;

class Collection extends PayoutCollection implements SearchResultInterface
{
    private AggregationInterface $aggregations;

    protected function _construct(): void
    {
        $this->_init(
            \Comave\PayoutManagement\Model\Payout::class,
            \Comave\PayoutManagement\Model\ResourceModel\Payout::class
        );
        $this->_map['fields']['entity_id'] = 'main_table.entity_id';
    }

    public function getAggregations(): AggregationInterface
    {
        return $this->aggregations;
    }

    /**
     * @param AggregationInterface $aggregations
     */
    public function setAggregations($aggregations): void
    {
        $this->aggregations = $aggregations;
    }

    public function getAllIds(?int $limit = null, ?int $offset = null): array
    {
        return $this->getConnection()->fetchCol(
            $this->_getAllIdsSelect($limit, $offset),
            $this->_bindParams
        );
    }

    public function getSearchCriteria(): ?SearchCriteriaInterface
    {
        return null;
    }

    public function setSearchCriteria($searchCriteria = null): SearchResultInterface
    {
        return $this;
    }

    public function getTotalCount(): int
    {
        return $this->getSize();
    }

    public function setTotalCount($totalCount): SearchResultInterface
    {
        return $this;
    }

    public function setItems($items = null): SearchResultInterface
    {
        return $this;
    }
}
