<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    
    <table name="comave_payout_management" resource="default" engine="innodb" comment="Payout Management Table">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="varchar" name="stripe_payout_id" nullable="false" length="255" comment="Stripe Payout ID"/>
        <column xsi:type="int" name="seller_id" unsigned="true" nullable="false" comment="Seller ID"/>
        <column xsi:type="varchar" name="seller_name" nullable="true" length="255" comment="Seller Name"/>
        <column xsi:type="varchar" name="stripe_account_id" nullable="false" length="255" comment="Stripe Account ID"/>
        <column xsi:type="decimal" name="amount" scale="4" precision="12" unsigned="false" nullable="false" comment="Payout Amount"/>
        <column xsi:type="varchar" name="currency" nullable="false" length="3" comment="Currency Code"/>
        <column xsi:type="varchar" name="status" nullable="false" length="50" comment="Payout Status"/>
        <column xsi:type="varchar" name="payment_method" nullable="false" length="100" default="stripe" comment="Payment Method"/>
        <column xsi:type="timestamp" name="scheduled_date" nullable="true" comment="Scheduled Payout Date"/>
        <column xsi:type="timestamp" name="completion_date" nullable="true" comment="Completion Date"/>
        <column xsi:type="timestamp" name="created_at" nullable="false" default="CURRENT_TIMESTAMP" comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" nullable="false" default="CURRENT_TIMESTAMP" on_update="true" comment="Updated At"/>
        <column xsi:type="timestamp" name="last_sync_at" nullable="true" comment="Last Sync with Stripe"/>
        
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        
        <constraint xsi:type="unique" referenceId="COMAVE_PAYOUT_MANAGEMENT_STRIPE_PAYOUT_ID">
            <column name="stripe_payout_id"/>
        </constraint>
        
        <index referenceId="COMAVE_PAYOUT_MANAGEMENT_SELLER_ID" indexType="btree">
            <column name="seller_id"/>
        </index>
        
        <index referenceId="COMAVE_PAYOUT_MANAGEMENT_STATUS" indexType="btree">
            <column name="status"/>
        </index>
        
        <index referenceId="COMAVE_PAYOUT_MANAGEMENT_SCHEDULED_DATE" indexType="btree">
            <column name="scheduled_date"/>
        </index>
        
        <index referenceId="COMAVE_PAYOUT_MANAGEMENT_COMPLETION_DATE" indexType="btree">
            <column name="completion_date"/>
        </index>
    </table>
    
</schema>
