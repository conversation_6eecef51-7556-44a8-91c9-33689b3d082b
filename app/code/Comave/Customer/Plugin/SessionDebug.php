<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Model\Session;

class SessionDebug
{
    public function afterSetCustomerAsLoggedIn(Session $subject, $result, $customer)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $logger->info('=== Customer Session - setCustomerAsLoggedIn ===');
        $logger->info('Customer ID: ' . ($customer ? $customer->getId() : 'NULL'));
        $logger->info('Customer Email: ' . ($customer ? $customer->getEmail() : 'NULL'));
        
        return $result;
    }
    
    public function afterLogout(Session $subject, $result)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $logger->info('=== Customer Session - logout ===');
        $logger->info('Customer logged out');
        
        // Get stack trace to see what called logout
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10);
        $logger->info('Logout called from: ' . print_r($trace, true));
        
        return $result;
    }
}
