<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Controller\Account\LogoutSuccess;
use Magento\Framework\Controller\ResultInterface;

class LogoutSuccessDebug
{
    public function beforeExecute(LogoutSuccess $subject): array
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $request = $subject->getRequest();
        $referer = $request->getServer('HTTP_REFERER');
        $requestUri = $request->getRequestUri();
        
        $logger->info('=== LogoutSuccess Controller - BEFORE Execute ===');
        $logger->info('Request URI: ' . ($requestUri ?: 'NULL'));
        $logger->info('Referer: ' . ($referer ?: 'NULL'));
        $logger->info('User Agent: ' . ($request->getServer('HTTP_USER_AGENT') ?: 'NULL'));
        
        // Log all request parameters
        $params = $request->getParams();
        $logger->info('Request params: ' . print_r($params, true));
        
        return [];
    }
    
    public function afterExecute(LogoutSuccess $subject, ResultInterface $result): ResultInterface
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $resultClass = get_class($result);
        
        $logger->info('=== LogoutSuccess Controller - AFTER Execute ===');
        $logger->info('Result class: ' . $resultClass);
        
        return $result;
    }
}
