<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Webkul\Marketplace\Controller\Account\Dashboard;
use Magento\Framework\Controller\ResultInterface;

class DashboardDebug
{
    public function beforeExecute(Dashboard $subject): array
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $request = $subject->getRequest();
        $referer = $request->getServer('HTTP_REFERER');
        $requestUri = $request->getRequestUri();
        
        $logger->info('=== Marketplace Dashboard Controller - BEFORE Execute ===');
        $logger->info('Request URI: ' . ($requestUri ?: 'NULL'));
        $logger->info('Referer: ' . ($referer ?: 'NULL'));
        
        return [];
    }
    
    public function afterExecute(Dashboard $subject, ResultInterface $result): ResultInterface
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $resultClass = get_class($result);
        
        $logger->info('=== Marketplace Dashboard Controller - AFTER Execute ===');
        $logger->info('Result class: ' . $resultClass);
        
        if (method_exists($result, 'getPath')) {
            $logger->info('Redirect path: ' . $result->getPath());
        }
        
        return $result;
    }
}
