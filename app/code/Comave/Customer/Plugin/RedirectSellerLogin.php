<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Controller\Account\LoginPost;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Controller\ResultInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Magento\Customer\Model\Session;

class RedirectSellerLogin
{
    private const SELLER_ACCOUNT_LOGIN = 'marketplace/account/login';

    public function __construct(
        private readonly RedirectFactory $resultRedirectFactory,
        private readonly RequestInterface $request,
        private readonly MarketplaceHelper $mpHelperData,
        private readonly Session $session
    ) {}

    /**
     * @param Login $subject
     * @param ResultInterface $result
     * @return ResultInterface
     */
    public function afterExecute(LoginPost $subject, ResultInterface $result): ResultInterface
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $customerId = $this->session->getCustomerId();
        $referer = $this->request->getServer('HTTP_REFERER');
        $resultClass = get_class($result);

        $logger->info('=== RedirectSellerLogin Plugin ===');
        $logger->info('Customer ID: ' . ($customerId ?: 'NULL'));
        $logger->info('Referer: ' . ($referer ?: 'NULL'));
        $logger->info('Result class: ' . $resultClass);
        $logger->info('Is logged in: ' . ($this->session->isLoggedIn() ? 'YES' : 'NO'));

        if (!$customerId) {
            $logger->info('Login failed - returning original result');
            return $result;
        }

        // Only redirect if login was successful and came from marketplace login page
        if ($referer && strpos($referer, '/marketplace/account/login') !== false) {
            $logger->info('Login successful from marketplace - redirecting to dashboard');
            $isSeller = $this->isSeller((int)$customerId);
            $this->session->setData('is_seller', $isSeller);
            $logger->info('Is seller: ' . ($isSeller ? 'YES' : 'NO'));

            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
        }

        $logger->info('Login successful but not from marketplace - returning original result');
        return $result;
    }

    /**
     * Check is seller
     * @param int $customerId
     * @return bool
     */
    private function isSeller(int $customerId): bool
    {
        return $this->mpHelperData->getSellerCollectionObj($customerId)
                ->addFieldToFilter('is_seller', true)
                ->getSize() > 0;
    }
}
