<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Controller\Account\LogoutSuccess;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Message\ManagerInterface;

class RedirectSellerLogout
{
    private const SELLER_ACCOUNT_LOGIN = 'marketplace/account/login';

    public function __construct(
        private readonly RedirectFactory $resultRedirectFactory,
        private readonly RequestInterface $request,
        private readonly ManagerInterface $messageManager
    ) {}

    /**
     * Redirect marketplace users to seller login after logout
     *
     * Uses afterExecute instead of aroundExecute for better performance.
     * The LogoutSuccess controller returns a Page result, which we intercept
     * and replace with a Redirect result when needed.
     *
     * @param LogoutSuccess $subject
     * @param ResultInterface $result
     * @return ResultInterface
     */
    public function afterExecute(LogoutSuccess $subject, ResultInterface $result): ResultInterface
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $referer = $this->request->getServer('HTTP_REFERER');
        $resultClass = get_class($result);

        $logger->info('=== RedirectSellerLogout Plugin ===');
        $logger->info('Referer: ' . ($referer ?: 'NULL'));
        $logger->info('Result class: ' . $resultClass);
        $logger->info('Plugin is DISABLED - should not execute');

        // Only redirect if the referer is specifically from marketplace pages and not login pages
        if ($referer && strpos($referer, '/marketplace/') !== false && strpos($referer, '/login') === false) {
            $logger->info('Would redirect and add logout message (but plugin is disabled)');
            $this->messageManager->addSuccessMessage(__('You have been logged out successfully.'));
            $resultRedirect = $this->resultRedirectFactory->create();
            $resultRedirect->setPath(self::SELLER_ACCOUNT_LOGIN);
            return $resultRedirect;
        }

        $logger->info('No redirect needed');
        return $result;
    }
}
