<?php

declare(strict_types=1);

namespace Comave\Customer\Plugin;

use Magento\Customer\Controller\Account\LoginPost;
use Magento\Framework\Controller\ResultInterface;

class LoginPostDebug
{
    public function beforeExecute(LoginPost $subject): array
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $request = $subject->getRequest();
        $loginData = $request->getPost('login');
        $vendorLogin = $request->getPost('vendor_login');
        $referer = $request->getServer('HTTP_REFERER');
        
        $logger->info('=== LoginPost Controller - BEFORE Execute ===');
        $logger->info('Referer: ' . ($referer ?: 'NULL'));
        $logger->info('Vendor login param: ' . ($vendorLogin ?: 'NULL'));
        $logger->info('Login username: ' . (isset($loginData['username']) ? $loginData['username'] : 'NULL'));
        $logger->info('Has password: ' . (isset($loginData['password']) && !empty($loginData['password']) ? 'YES' : 'NO'));
        
        return [];
    }
    
    public function afterExecute(LoginPost $subject, ResultInterface $result): ResultInterface
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $resultClass = get_class($result);
        
        $logger->info('=== LoginPost Controller - AFTER Execute ===');
        $logger->info('Result class: ' . $resultClass);
        
        if (method_exists($result, 'getPath')) {
            $logger->info('Redirect path: ' . $result->getPath());
        }
        
        return $result;
    }
}
