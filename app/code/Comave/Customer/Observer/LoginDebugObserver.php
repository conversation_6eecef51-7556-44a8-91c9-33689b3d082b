<?php

declare(strict_types=1);

namespace Comave\Customer\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class LoginDebugObserver implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_login_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        
        $event = $observer->getEvent();
        $customer = $event->getCustomer();
        
        $logger->info('=== Core Customer Login Event ===');
        $logger->info('Customer ID: ' . ($customer ? $customer->getId() : 'NULL'));
        $logger->info('Customer Email: ' . ($customer ? $customer->getEmail() : 'NULL'));
        $logger->info('Event Name: customer_login');
        
        return $this;
    }
}
